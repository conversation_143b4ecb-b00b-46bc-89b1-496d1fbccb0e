<template>
  <div class="virtual-task-list" ref="containerRef">
    <!-- 虚拟滚动容器 -->
    <div 
      class="virtual-scroll-container"
      :style="{ height: containerHeight + 'px' }"
      @scroll="handleScroll"
    >
      <!-- 占位空间 - 上方 -->
      <div :style="{ height: offsetY + 'px' }"></div>
      
      <!-- 可见任务列表 -->
      <div class="visible-tasks">
        <template v-for="(item, index) in visibleItems" :key="item.id">
          <!-- 批次管理器 -->
          <BatchManagerItem 
            v-if="item.type === 'batch-manager'"
            :batch-manager="item"
            :expanded="expandedItems.has(item.id)"
            @toggle-expanded="toggleExpanded(item.id)"
            @pause="pauseBatchManager"
            @resume="resumeBatchManager"
            @cancel="cancelBatchManager"
          />
          
          <!-- 批量任务 -->
          <BatchTaskItem
            v-else-if="item.type === 'batch'"
            :batch-task="item"
            :sub-tasks="getSubTasks(item.id)"
            :expanded="expandedItems.has(item.id)"
            @toggle-expanded="toggleExpanded(item.id)"
            @pause="pauseBatchTask"
            @resume="resumeBatchTask"
            @cancel="cancelBatchTask"
          />
          
          <!-- 单独任务 -->
          <TaskItem
            v-else
            :task="item"
            @pause="pauseTask"
            @resume="resumeTask"
            @cancel="cancelTask"
            @retry="retryTask"
          />
        </template>
      </div>
      
      <!-- 占位空间 - 下方 -->
      <div :style="{ height: (totalHeight - offsetY - visibleHeight) + 'px' }"></div>
    </div>
    
    <!-- 加载更多指示器 -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>
    
    <!-- 性能统计 -->
    <div v-if="showPerformanceStats" class="performance-stats">
      <div class="stat-item">
        <span>总任务数:</span>
        <span>{{ totalItems }}</span>
      </div>
      <div class="stat-item">
        <span>可见任务数:</span>
        <span>{{ visibleItems.length }}</span>
      </div>
      <div class="stat-item">
        <span>渲染时间:</span>
        <span>{{ renderTime }}ms</span>
      </div>
      <div class="stat-item">
        <span>内存使用:</span>
        <span>{{ memoryUsage }}MB</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useTusUpload } from './composables/useTusUpload'
import { useVirtualScroll } from './composables/useVirtualScroll'
import { usePerformanceMonitor } from './composables/usePerformanceMonitor'
import BatchManagerItem from './BatchManagerItem.vue'
import BatchTaskItem from './BatchTaskItem.vue'
import TaskItem from './TaskItem.vue'
import type { UploadTask, BatchUploadTask, BatchManager } from './composables/useTusUpload'

interface Props {
  containerHeight?: number
  itemHeight?: number
  bufferSize?: number
  showPerformanceStats?: boolean
  enableLazyLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  containerHeight: 400,
  itemHeight: 80,
  bufferSize: 5,
  showPerformanceStats: false,
  enableLazyLoading: true
})

const emit = defineEmits<{
  'scroll-end': []
  'performance-warning': [metric: string, value: number]
}>()

// 组合式API
const tusUpload = useTusUpload()
const containerRef = ref<HTMLElement>()
const expandedItems = ref<Set<string>>(new Set())
const loading = ref(false)

// 获取所有任务数据
const allTasks = computed(() => {
  const tasks: (UploadTask | BatchUploadTask | BatchManager)[] = []
  
  // 添加批次管理器
  tasks.push(...tusUpload.batchManagers.value)
  
  // 添加批量任务
  tasks.push(...tusUpload.batchTasks.value)
  
  // 添加独立任务
  tasks.push(...tusUpload.standaloneTasks.value)
  
  return tasks.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
})

// 虚拟滚动
const {
  visibleItems,
  offsetY,
  totalHeight,
  visibleHeight,
  handleScroll,
  scrollToItem,
  updateItemHeight
} = useVirtualScroll({
  items: allTasks,
  containerHeight: props.containerHeight,
  itemHeight: props.itemHeight,
  bufferSize: props.bufferSize
})

// 性能监控
const {
  renderTime,
  memoryUsage,
  startMeasure,
  endMeasure,
  checkMemoryUsage
} = usePerformanceMonitor()

// 计算属性
const totalItems = computed(() => allTasks.value.length)

// 方法
const toggleExpanded = (itemId: string) => {
  if (expandedItems.value.has(itemId)) {
    expandedItems.value.delete(itemId)
  } else {
    expandedItems.value.add(itemId)
  }
  
  // 更新项目高度（展开的项目更高）
  const newHeight = expandedItems.value.has(itemId) ? props.itemHeight * 2 : props.itemHeight
  updateItemHeight(itemId, newHeight)
}

const getSubTasks = (batchId: string) => {
  return tusUpload.getBatchSubTasks(batchId)
}

// 任务操作方法
const pauseBatchManager = async (id: string) => {
  await tusUpload.pauseBatchManager(id)
}

const resumeBatchManager = async (id: string) => {
  await tusUpload.resumeBatchManager(id)
}

const cancelBatchManager = async (id: string) => {
  await tusUpload.cancelBatchManager(id)
}

const pauseBatchTask = async (id: string) => {
  await tusUpload.pauseBatchUpload(id)
}

const resumeBatchTask = async (id: string) => {
  await tusUpload.resumeBatchUpload(id)
}

const cancelBatchTask = async (id: string) => {
  await tusUpload.deleteBatchTask(id)
}

const pauseTask = async (id: string) => {
  await tusUpload.pauseUpload(id)
}

const resumeTask = async (id: string) => {
  await tusUpload.resumeUpload(id)
}

const cancelTask = async (id: string) => {
  await tusUpload.cancelUpload(id)
}

const retryTask = async (id: string) => {
  await tusUpload.retryUpload(id)
}

// 性能优化：防抖更新
const debouncedUpdate = (() => {
  let timeoutId: NodeJS.Timeout
  return () => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      startMeasure('render')
      nextTick(() => {
        endMeasure('render')
      })
    }, 16) // 约60fps
  }
})()

// 监听数据变化
watch(allTasks, debouncedUpdate, { deep: false })

// 内存监控
const memoryCheckInterval = ref<NodeJS.Timeout>()

onMounted(() => {
  // 启动内存监控
  memoryCheckInterval.value = setInterval(() => {
    const usage = checkMemoryUsage()
    if (usage > 100) { // 超过100MB警告
      emit('performance-warning', 'memory', usage)
    }
  }, 5000)
})

onUnmounted(() => {
  if (memoryCheckInterval.value) {
    clearInterval(memoryCheckInterval.value)
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  toggleExpanded,
  getPerformanceStats: () => ({
    totalItems: totalItems.value,
    visibleItems: visibleItems.value.length,
    renderTime: renderTime.value,
    memoryUsage: memoryUsage.value
  })
})
</script>

<style scoped>
.virtual-task-list {
  position: relative;
  width: 100%;
}

.virtual-scroll-container {
  overflow-y: auto;
  overflow-x: hidden;
}

.visible-tasks {
  position: relative;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 10px;
  color: #666;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.performance-stats {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 4px;
}

.stat-item:last-child {
  margin-bottom: 0;
}
</style>
