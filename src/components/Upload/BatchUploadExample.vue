<template>
  <div class="batch-upload-example">
    <div class="config-section">
      <h3>分批上传配置</h3>
      <div class="config-grid">
        <div class="config-item">
          <label>启用分批上传</label>
          <input 
            type="checkbox" 
            v-model="config.enableBatching"
            @change="updateConfig"
          />
        </div>
        
        <div class="config-item">
          <label>分批阈值 (文件数)</label>
          <input 
            type="number" 
            v-model.number="config.batchThreshold"
            @change="updateConfig"
            min="10"
            max="1000"
          />
        </div>
        
        <div class="config-item">
          <label>批次大小 (文件数)</label>
          <input 
            type="number" 
            v-model.number="config.batchSize"
            @change="updateConfig"
            min="5"
            max="50"
          />
        </div>
        
        <div class="config-item">
          <label>批次延迟 (毫秒)</label>
          <input 
            type="number" 
            v-model.number="config.batchDelay"
            @change="updateConfig"
            min="0"
            max="10000"
            step="500"
          />
        </div>
        
        <div class="config-item">
          <label>动态批次大小</label>
          <input 
            type="checkbox" 
            v-model="config.dynamicBatchSize"
            @change="updateConfig"
          />
        </div>
      </div>
    </div>

    <div class="status-section">
      <h3>上传状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <span>批次管理器数量:</span>
          <span>{{ batchManagers.length }}</span>
        </div>
        
        <div class="status-item">
          <span>活跃任务数量:</span>
          <span>{{ activeTasks.length }}</span>
        </div>
        
        <div class="status-item">
          <span>总体进度:</span>
          <span>{{ Math.round(totalProgress) }}%</span>
        </div>
      </div>
    </div>

    <div class="batch-managers-section" v-if="batchManagers.length > 0">
      <h3>分批上传任务</h3>
      <div class="batch-manager-list">
        <div 
          v-for="manager in batchManagers" 
          :key="manager.id"
          class="batch-manager-item"
        >
          <div class="batch-manager-header">
            <div class="batch-info">
              <span class="batch-title">
                分批上传 - {{ manager.totalFiles }} 个文件
              </span>
              <span class="batch-progress">
                第{{ manager.currentBatch }}批/共{{ manager.totalBatches }}批 ({{ manager.progress }}%)
              </span>
            </div>
            
            <div class="batch-actions">
              <button 
                v-if="manager.status === 'processing'"
                @click="pauseBatchManager(manager.id)"
                class="action-btn pause-btn"
              >
                暂停
              </button>
              
              <button 
                v-if="manager.status === 'paused'"
                @click="resumeBatchManager(manager.id)"
                class="action-btn resume-btn"
              >
                恢复
              </button>
              
              <button 
                @click="cancelBatchManager(manager.id)"
                class="action-btn cancel-btn"
              >
                取消
              </button>
            </div>
          </div>
          
          <div class="batch-progress-bar">
            <div 
              class="progress-fill"
              :style="{ width: manager.progress + '%' }"
            ></div>
          </div>
          
          <div class="batch-status">
            <span :class="['status-badge', manager.status]">
              {{ getStatusText(manager.status) }}
            </span>
            <span class="batch-time">
              开始时间: {{ formatTime(manager.startTime) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>测试分批上传</h3>
      <p class="test-description">
        选择大量文件（超过 {{ config.batchThreshold }} 个）来测试分批上传功能
      </p>
      <input 
        type="file" 
        multiple 
        @change="handleFileSelect"
        class="file-input"
      />
      <button 
        v-if="selectedFiles.length > 0"
        @click="startBatchUpload"
        class="upload-btn"
        :disabled="uploading"
      >
        {{ uploading ? '上传中...' : `上传 ${selectedFiles.length} 个文件` }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTusUpload } from './composables/useTusUpload'
import type { BatchUploadConfig } from './composables/useTusUpload'

const tusUpload = useTusUpload()

// 配置状态
const config = ref<BatchUploadConfig>({
  enableBatching: true,
  batchThreshold: 50,
  batchSize: 15,
  batchDelay: 2000,
  dynamicBatchSize: true,
  maxBatchSize: 25,
  minBatchSize: 5,
})

// 测试状态
const selectedFiles = ref<File[]>([])
const uploading = ref(false)

// 计算属性
const batchManagers = computed(() => tusUpload.batchManagers.value)
const activeTasks = computed(() => tusUpload.activeTasks.value)
const totalProgress = computed(() => tusUpload.totalProgress.value)

// 方法
const updateConfig = () => {
  tusUpload.updateBatchConfig(config.value)
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    selectedFiles.value = Array.from(target.files)
  }
}

const startBatchUpload = async () => {
  if (selectedFiles.value.length === 0) return
  
  uploading.value = true
  
  try {
    await tusUpload.uploadFiles(selectedFiles.value, {
      category: 'test',
      source: 'batch-upload-example'
    })
  } catch (error) {
    console.error('批量上传失败:', error)
  } finally {
    uploading.value = false
    selectedFiles.value = []
  }
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '等待中',
    processing: '处理中',
    paused: '已暂停',
    completed: '已完成',
    error: '出错',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  // 初始化配置
  updateConfig()
})
</script>

<style scoped>
.batch-upload-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.config-section,
.status-section,
.batch-managers-section,
.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.config-grid,
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.config-item,
.status-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.config-item label {
  font-weight: 500;
  color: #333;
}

.config-item input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.status-item {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 4px;
}

.batch-manager-item {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.batch-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.batch-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.batch-title {
  font-weight: 600;
  color: #333;
}

.batch-progress {
  font-size: 14px;
  color: #666;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.pause-btn { background: #ff9800; color: white; }
.resume-btn { background: #4caf50; color: white; }
.cancel-btn { background: #f44336; color: white; }

.batch-progress-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: #2196f3;
  transition: width 0.3s ease;
}

.batch-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending { background: #fff3cd; color: #856404; }
.status-badge.processing { background: #d1ecf1; color: #0c5460; }
.status-badge.paused { background: #f8d7da; color: #721c24; }
.status-badge.completed { background: #d4edda; color: #155724; }
.status-badge.error { background: #f8d7da; color: #721c24; }
.status-badge.cancelled { background: #e2e3e5; color: #383d41; }

.batch-time {
  font-size: 12px;
  color: #666;
}

.test-description {
  margin-bottom: 15px;
  color: #666;
}

.file-input {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}

.upload-btn {
  padding: 10px 20px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.upload-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
