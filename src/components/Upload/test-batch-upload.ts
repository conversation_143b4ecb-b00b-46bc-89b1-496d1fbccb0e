/**
 * 分批上传功能测试脚本
 * 
 * 这个脚本用于测试分批上传功能的各种场景
 * 注意：这是一个测试脚本，不应该在生产环境中使用
 */

import { useTusUpload } from './composables/useTusUpload'
import type { BatchUploadConfig } from './composables/useTusUpload'

// 创建模拟文件的辅助函数
function createMockFile(name: string, size: number = 1024): File {
  const content = new Array(size).fill('a').join('')
  const blob = new Blob([content], { type: 'text/plain' })
  return new File([blob], name, { type: 'text/plain' })
}

// 创建大量模拟文件
function createMockFiles(count: number, prefix: string = 'test-file'): File[] {
  const files: File[] = []
  for (let i = 1; i <= count; i++) {
    const fileName = `${prefix}-${i.toString().padStart(3, '0')}.txt`
    const fileSize = Math.floor(Math.random() * 10000) + 1000 // 1KB - 10KB
    files.push(createMockFile(fileName, fileSize))
  }
  return files
}

// 测试配置
const testConfigs: { name: string; config: Partial<BatchUploadConfig>; fileCount: number }[] = [
  {
    name: '默认配置测试',
    config: {},
    fileCount: 60
  },
  {
    name: '小批次测试',
    config: {
      batchThreshold: 20,
      batchSize: 5,
      batchDelay: 1000
    },
    fileCount: 25
  },
  {
    name: '大批次测试',
    config: {
      batchThreshold: 30,
      batchSize: 20,
      batchDelay: 500,
      dynamicBatchSize: false
    },
    fileCount: 100
  },
  {
    name: '动态批次大小测试',
    config: {
      batchThreshold: 40,
      batchSize: 15,
      dynamicBatchSize: true,
      maxBatchSize: 30,
      minBatchSize: 8
    },
    fileCount: 80
  }
]

// 测试结果接口
interface TestResult {
  configName: string
  success: boolean
  error?: string
  duration: number
  fileCount: number
  batchCount: number
  completedFiles: number
  failedFiles: number
}

// 主测试函数
export async function runBatchUploadTests(): Promise<TestResult[]> {
  const tusUpload = useTusUpload()
  const results: TestResult[] = []

  console.log('🚀 开始分批上传功能测试...')

  for (const testConfig of testConfigs) {
    console.log(`\n📋 测试配置: ${testConfig.name}`)
    console.log(`文件数量: ${testConfig.fileCount}`)
    console.log(`配置:`, testConfig.config)

    const startTime = Date.now()
    let testResult: TestResult = {
      configName: testConfig.name,
      success: false,
      duration: 0,
      fileCount: testConfig.fileCount,
      batchCount: 0,
      completedFiles: 0,
      failedFiles: 0
    }

    try {
      // 更新配置
      tusUpload.updateBatchConfig(testConfig.config)

      // 创建测试文件
      const files = createMockFiles(testConfig.fileCount, `test-${testConfig.name.replace(/\s+/g, '-')}`)
      console.log(`✅ 创建了 ${files.length} 个测试文件`)

      // 设置回调来跟踪进度
      let completedCount = 0
      let errorCount = 0

      const callbacks = {
        onFileUploaded: (file: File) => {
          completedCount++
          console.log(`📁 文件上传完成: ${file.name} (${completedCount}/${files.length})`)
        },
        onAllFilesUploaded: (files: File[]) => {
          console.log(`🎉 所有文件上传完成: ${files.length} 个文件`)
        },
        onUploadError: (error: string, failedTasks: any[]) => {
          errorCount += failedTasks.length
          console.error(`❌ 上传错误: ${error}, 失败任务数: ${failedTasks.length}`)
        }
      }

      // 开始上传
      console.log(`🔄 开始上传...`)
      await tusUpload.uploadFiles(files, {
        category: 'test',
        source: 'batch-upload-test',
        testConfig: testConfig.name
      }, callbacks)

      // 等待一段时间确保所有任务完成
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 检查批次管理器状态
      const batchManagers = tusUpload.batchManagers.value
      const relevantManager = batchManagers.find(m => 
        m.metadata?.testConfig === testConfig.name
      )

      if (relevantManager) {
        testResult.batchCount = relevantManager.totalBatches
        console.log(`📊 批次信息: ${relevantManager.totalBatches} 个批次`)
        console.log(`📈 整体进度: ${relevantManager.progress}%`)
        console.log(`📋 当前状态: ${relevantManager.status}`)
      }

      testResult.completedFiles = completedCount
      testResult.failedFiles = errorCount
      testResult.success = true

      console.log(`✅ 测试完成: ${testConfig.name}`)
      console.log(`   - 完成文件: ${completedCount}`)
      console.log(`   - 失败文件: ${errorCount}`)

    } catch (error) {
      testResult.error = error instanceof Error ? error.message : String(error)
      console.error(`❌ 测试失败: ${testConfig.name}`, error)
    }

    testResult.duration = Date.now() - startTime
    results.push(testResult)

    // 测试间隔
    console.log(`⏳ 等待 3 秒后进行下一个测试...`)
    await new Promise(resolve => setTimeout(resolve, 3000))
  }

  return results
}

// 打印测试结果
export function printTestResults(results: TestResult[]): void {
  console.log('\n📊 测试结果汇总:')
  console.log('=' .repeat(80))

  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.configName}`)
    console.log(`   状态: ${result.success ? '✅ 成功' : '❌ 失败'}`)
    console.log(`   耗时: ${(result.duration / 1000).toFixed(2)} 秒`)
    console.log(`   文件: ${result.fileCount} 个`)
    console.log(`   批次: ${result.batchCount} 个`)
    console.log(`   完成: ${result.completedFiles} 个`)
    console.log(`   失败: ${result.failedFiles} 个`)
    
    if (result.error) {
      console.log(`   错误: ${result.error}`)
    }
  })

  const successCount = results.filter(r => r.success).length
  const totalFiles = results.reduce((sum, r) => sum + r.fileCount, 0)
  const totalCompleted = results.reduce((sum, r) => sum + r.completedFiles, 0)
  const totalFailed = results.reduce((sum, r) => sum + r.failedFiles, 0)

  console.log('\n📈 总体统计:')
  console.log(`   测试通过率: ${successCount}/${results.length} (${((successCount / results.length) * 100).toFixed(1)}%)`)
  console.log(`   文件总数: ${totalFiles} 个`)
  console.log(`   完成总数: ${totalCompleted} 个`)
  console.log(`   失败总数: ${totalFailed} 个`)
  console.log(`   成功率: ${((totalCompleted / totalFiles) * 100).toFixed(1)}%`)
}

// 简单的性能测试
export async function runPerformanceTest(): Promise<void> {
  console.log('\n🚀 开始性能测试...')
  
  const tusUpload = useTusUpload()
  
  // 测试不同文件数量的性能
  const fileCounts = [10, 50, 100, 200, 500]
  
  for (const fileCount of fileCounts) {
    console.log(`\n📊 测试 ${fileCount} 个文件的性能...`)
    
    const files = createMockFiles(fileCount, `perf-test`)
    const startTime = Date.now()
    
    try {
      await tusUpload.uploadFiles(files, {
        category: 'performance-test',
        source: 'performance-test'
      })
      
      const duration = Date.now() - startTime
      const avgTimePerFile = duration / fileCount
      
      console.log(`✅ ${fileCount} 个文件上传完成`)
      console.log(`   总耗时: ${(duration / 1000).toFixed(2)} 秒`)
      console.log(`   平均每文件: ${avgTimePerFile.toFixed(2)} 毫秒`)
      console.log(`   吞吐量: ${(fileCount / (duration / 1000)).toFixed(2)} 文件/秒`)
      
    } catch (error) {
      console.error(`❌ ${fileCount} 个文件测试失败:`, error)
    }
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 2000))
  }
}

// 导出便捷的测试运行函数
export async function runAllTests(): Promise<void> {
  try {
    // 运行功能测试
    const results = await runBatchUploadTests()
    printTestResults(results)
    
    // 运行性能测试
    await runPerformanceTest()
    
    console.log('\n🎉 所有测试完成!')
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error)
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && (window as any).__runBatchUploadTests) {
  runAllTests()
}
