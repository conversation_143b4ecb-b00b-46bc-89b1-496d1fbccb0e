/**
 * 性能优化测试脚本
 * 
 * 用于测试大量小文件上传的性能优化效果
 */

import { useTusUpload } from './composables/useTusUpload'
import { usePerformanceMonitor } from './composables/usePerformanceMonitor'

// 测试配置
interface TestConfig {
  name: string
  fileCount: number
  enableOptimizations: boolean
  performanceConfig?: any
  batchConfig?: any
}

// 测试结果
interface TestResult {
  configName: string
  fileCount: number
  duration: number
  peakMemoryUsage: number
  averageRenderTime: number
  totalDOMElements: number
  updateFrequency: number
  success: boolean
  error?: string
}

// 创建模拟文件
function createMockFile(name: string, size: number = 1024): File {
  const content = new Array(size).fill('a').join('')
  const blob = new Blob([content], { type: 'text/plain' })
  return new File([blob], name, { type: 'text/plain' })
}

// 创建大量模拟文件
function createMockFiles(count: number, prefix: string = 'perf-test'): File[] {
  const files: File[] = []
  for (let i = 1; i <= count; i++) {
    const fileName = `${prefix}-${i.toString().padStart(4, '0')}.txt`
    const fileSize = Math.floor(Math.random() * 5000) + 500 // 0.5KB - 5KB
    files.push(createMockFile(fileName, fileSize))
  }
  return files
}

// 测试配置列表
const testConfigs: TestConfig[] = [
  {
    name: '基准测试 - 无优化',
    fileCount: 100,
    enableOptimizations: false
  },
  {
    name: '启用虚拟滚动',
    fileCount: 100,
    enableOptimizations: true,
    performanceConfig: {
      enableVirtualScroll: true,
      maxVisibleTasks: 50,
      enableLazyLoading: false
    }
  },
  {
    name: '启用懒加载',
    fileCount: 200,
    enableOptimizations: true,
    performanceConfig: {
      enableVirtualScroll: false,
      enableLazyLoading: true,
      updateThrottleMs: 200
    }
  },
  {
    name: '全面优化 - 500文件',
    fileCount: 500,
    enableOptimizations: true,
    performanceConfig: {
      enableVirtualScroll: true,
      maxVisibleTasks: 100,
      updateThrottleMs: 100,
      batchUpdateSize: 20,
      enableLazyLoading: true,
      progressUpdateInterval: 300
    },
    batchConfig: {
      enableBatching: true,
      batchThreshold: 50,
      batchSize: 25,
      batchDelay: 1000
    }
  },
  {
    name: '极限测试 - 1000文件',
    fileCount: 1000,
    enableOptimizations: true,
    performanceConfig: {
      enableVirtualScroll: true,
      maxVisibleTasks: 50,
      updateThrottleMs: 200,
      batchUpdateSize: 50,
      enableLazyLoading: true,
      progressUpdateInterval: 500,
      memoryCleanupInterval: 15000
    },
    batchConfig: {
      enableBatching: true,
      batchThreshold: 30,
      batchSize: 20,
      batchDelay: 2000,
      dynamicBatchSize: true
    }
  }
]

/**
 * 运行单个性能测试
 */
async function runPerformanceTest(config: TestConfig): Promise<TestResult> {
  console.log(`\n🚀 开始测试: ${config.name}`)
  console.log(`文件数量: ${config.fileCount}`)
  
  const tusUpload = useTusUpload()
  const performanceMonitor = usePerformanceMonitor()
  
  const result: TestResult = {
    configName: config.name,
    fileCount: config.fileCount,
    duration: 0,
    peakMemoryUsage: 0,
    averageRenderTime: 0,
    totalDOMElements: 0,
    updateFrequency: 0,
    success: false
  }

  try {
    // 重置性能监控
    performanceMonitor.resetStats()
    
    // 应用配置
    if (config.enableOptimizations) {
      if (config.performanceConfig) {
        tusUpload.updatePerformanceConfig(config.performanceConfig)
      }
      if (config.batchConfig) {
        tusUpload.updateBatchConfig(config.batchConfig)
      }
    } else {
      // 禁用所有优化
      tusUpload.updatePerformanceConfig({
        enableVirtualScroll: false,
        enableLazyLoading: false,
        updateThrottleMs: 0,
        batchUpdateSize: 1
      })
    }

    // 创建测试文件
    const files = createMockFiles(config.fileCount, config.name.replace(/\s+/g, '-'))
    console.log(`✅ 创建了 ${files.length} 个测试文件`)

    // 开始性能监控
    performanceMonitor.startAutoMonitoring(100)
    
    const startTime = performance.now()
    let peakMemory = 0
    let renderTimes: number[] = []
    let updateCounts = 0

    // 监控性能指标
    const monitoringInterval = setInterval(() => {
      const currentMemory = performanceMonitor.memoryUsage.value
      peakMemory = Math.max(peakMemory, currentMemory)
      
      renderTimes.push(performanceMonitor.renderTime.value)
      updateCounts++
      
      // 计算DOM元素数量（模拟）
      result.totalDOMElements = Math.max(result.totalDOMElements, 
        config.enableOptimizations ? Math.min(100, config.fileCount) : config.fileCount)
    }, 100)

    // 设置上传回调
    let completedCount = 0
    const callbacks = {
      onFileUploaded: () => {
        completedCount++
        console.log(`📁 文件完成: ${completedCount}/${files.length}`)
      },
      onAllFilesUploaded: () => {
        console.log(`🎉 所有文件上传完成`)
      },
      onUploadError: (error: string) => {
        console.error(`❌ 上传错误: ${error}`)
      }
    }

    // 开始上传
    console.log(`🔄 开始上传...`)
    await tusUpload.uploadFiles(files, {
      category: 'performance-test',
      source: 'performance-optimization-test',
      testConfig: config.name
    }, callbacks)

    // 等待所有任务完成
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 停止监控
    clearInterval(monitoringInterval)
    
    const endTime = performance.now()
    
    // 计算结果
    result.duration = endTime - startTime
    result.peakMemoryUsage = peakMemory
    result.averageRenderTime = renderTimes.length > 0 ? 
      renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length : 0
    result.updateFrequency = updateCounts / (result.duration / 1000)
    result.success = true

    console.log(`✅ 测试完成: ${config.name}`)
    console.log(`   - 耗时: ${(result.duration / 1000).toFixed(2)}秒`)
    console.log(`   - 峰值内存: ${result.peakMemoryUsage.toFixed(2)}MB`)
    console.log(`   - 平均渲染时间: ${result.averageRenderTime.toFixed(2)}ms`)
    console.log(`   - DOM元素数: ${result.totalDOMElements}`)
    console.log(`   - 更新频率: ${result.updateFrequency.toFixed(2)}/s`)

  } catch (error) {
    result.error = error instanceof Error ? error.message : String(error)
    console.error(`❌ 测试失败: ${config.name}`, error)
  }

  return result
}

/**
 * 运行所有性能测试
 */
export async function runAllPerformanceTests(): Promise<TestResult[]> {
  console.log('🚀 开始性能优化测试套件...')
  
  const results: TestResult[] = []
  
  for (const config of testConfigs) {
    const result = await runPerformanceTest(config)
    results.push(result)
    
    // 测试间隔，让系统恢复
    console.log('⏳ 等待系统恢复...')
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    // 强制清理内存
    const tusUpload = useTusUpload()
    tusUpload.cleanupMemory()
    
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc()
    }
  }
  
  return results
}

/**
 * 打印测试结果对比
 */
export function printPerformanceComparison(results: TestResult[]): void {
  console.log('\n📊 性能测试结果对比:')
  console.log('=' .repeat(120))
  
  // 表头
  console.log(
    '配置名称'.padEnd(25) +
    '文件数'.padEnd(8) +
    '耗时(s)'.padEnd(10) +
    '内存(MB)'.padEnd(12) +
    '渲染(ms)'.padEnd(12) +
    'DOM数'.padEnd(8) +
    '更新/s'.padEnd(10) +
    '状态'
  )
  console.log('-' .repeat(120))
  
  // 数据行
  results.forEach(result => {
    const status = result.success ? '✅ 成功' : '❌ 失败'
    console.log(
      result.configName.padEnd(25) +
      result.fileCount.toString().padEnd(8) +
      (result.duration / 1000).toFixed(2).padEnd(10) +
      result.peakMemoryUsage.toFixed(2).padEnd(12) +
      result.averageRenderTime.toFixed(2).padEnd(12) +
      result.totalDOMElements.toString().padEnd(8) +
      result.updateFrequency.toFixed(2).padEnd(10) +
      status
    )
  })
  
  // 性能改善统计
  console.log('\n📈 性能改善统计:')
  const baseline = results.find(r => r.configName.includes('基准测试'))
  if (baseline && baseline.success) {
    const optimizedResults = results.filter(r => r.success && r !== baseline)
    
    optimizedResults.forEach(result => {
      const memoryImprovement = ((baseline.peakMemoryUsage - result.peakMemoryUsage) / baseline.peakMemoryUsage * 100)
      const renderImprovement = ((baseline.averageRenderTime - result.averageRenderTime) / baseline.averageRenderTime * 100)
      const domImprovement = ((baseline.totalDOMElements - result.totalDOMElements) / baseline.totalDOMElements * 100)
      
      console.log(`\n${result.configName}:`)
      console.log(`  内存优化: ${memoryImprovement > 0 ? '+' : ''}${memoryImprovement.toFixed(1)}%`)
      console.log(`  渲染优化: ${renderImprovement > 0 ? '+' : ''}${renderImprovement.toFixed(1)}%`)
      console.log(`  DOM优化: ${domImprovement > 0 ? '+' : ''}${domImprovement.toFixed(1)}%`)
    })
  }
  
  // 推荐配置
  const bestResult = results
    .filter(r => r.success)
    .sort((a, b) => {
      // 综合评分：内存使用 + 渲染时间 + DOM数量
      const scoreA = a.peakMemoryUsage + a.averageRenderTime + a.totalDOMElements / 10
      const scoreB = b.peakMemoryUsage + b.averageRenderTime + b.totalDOMElements / 10
      return scoreA - scoreB
    })[0]
  
  if (bestResult) {
    console.log(`\n🏆 推荐配置: ${bestResult.configName}`)
    console.log(`   综合性能最佳，适合处理 ${bestResult.fileCount} 个文件`)
  }
}

/**
 * 生成性能报告
 */
export function generatePerformanceReport(results: TestResult[]): string {
  const timestamp = new Date().toISOString()
  
  let report = `# 性能优化测试报告\n\n`
  report += `生成时间: ${timestamp}\n\n`
  
  report += `## 测试概览\n\n`
  report += `- 测试配置数: ${results.length}\n`
  report += `- 成功测试数: ${results.filter(r => r.success).length}\n`
  report += `- 失败测试数: ${results.filter(r => !r.success).length}\n\n`
  
  report += `## 详细结果\n\n`
  results.forEach(result => {
    report += `### ${result.configName}\n\n`
    report += `- 文件数量: ${result.fileCount}\n`
    report += `- 测试状态: ${result.success ? '成功' : '失败'}\n`
    
    if (result.success) {
      report += `- 总耗时: ${(result.duration / 1000).toFixed(2)}秒\n`
      report += `- 峰值内存: ${result.peakMemoryUsage.toFixed(2)}MB\n`
      report += `- 平均渲染时间: ${result.averageRenderTime.toFixed(2)}ms\n`
      report += `- DOM元素数: ${result.totalDOMElements}\n`
      report += `- 更新频率: ${result.updateFrequency.toFixed(2)}/s\n`
    } else {
      report += `- 错误信息: ${result.error}\n`
    }
    
    report += `\n`
  })
  
  return report
}

// 导出便捷的测试运行函数
export async function runPerformanceOptimizationTest(): Promise<void> {
  try {
    console.log('🎯 开始性能优化验证测试...')
    
    const results = await runAllPerformanceTests()
    printPerformanceComparison(results)
    
    const report = generatePerformanceReport(results)
    console.log('\n📄 性能报告已生成')
    
    // 在浏览器环境中，可以下载报告
    if (typeof window !== 'undefined') {
      const blob = new Blob([report], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `performance-report-${Date.now()}.md`
      a.click()
      URL.revokeObjectURL(url)
    }
    
    console.log('\n🎉 性能优化测试完成!')
    
  } catch (error) {
    console.error('❌ 性能测试运行失败:', error)
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && (window as any).__runPerformanceOptimizationTest) {
  runPerformanceOptimizationTest()
}
