import { ref, computed, onUnmounted } from 'vue'

// 性能指标接口
export interface PerformanceMetrics {
  renderTime: number
  memoryUsage: number
  cpuUsage: number
  taskCount: number
  updateFrequency: number
  frameRate: number
}

// 性能警告阈值
export interface PerformanceThresholds {
  maxRenderTime: number // 最大渲染时间 (ms)
  maxMemoryUsage: number // 最大内存使用 (MB)
  minFrameRate: number // 最小帧率 (fps)
  maxTaskCount: number // 最大任务数量
  maxUpdateFrequency: number // 最大更新频率 (updates/sec)
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  maxRenderTime: 16, // 60fps = 16.67ms per frame
  maxMemoryUsage: 200, // 200MB
  minFrameRate: 30, // 30fps
  maxTaskCount: 1000, // 1000个任务
  maxUpdateFrequency: 60 // 60 updates/sec
}

export function usePerformanceMonitor(thresholds: Partial<PerformanceThresholds> = {}) {
  const config = { ...DEFAULT_THRESHOLDS, ...thresholds }
  
  // 性能指标状态
  const renderTime = ref(0)
  const memoryUsage = ref(0)
  const cpuUsage = ref(0)
  const frameRate = ref(60)
  const updateCount = ref(0)
  const lastUpdateTime = ref(0)
  
  // 测量相关
  const measurements = ref<Map<string, number>>(new Map())
  const frameTimestamps = ref<number[]>([])
  const updateTimestamps = ref<number[]>([])
  
  // 警告状态
  const warnings = ref<Set<string>>(new Set())
  const performanceIssues = ref<string[]>([])
  
  // 开始测量
  const startMeasure = (name: string) => {
    measurements.value.set(name, performance.now())
  }
  
  // 结束测量
  const endMeasure = (name: string) => {
    const startTime = measurements.value.get(name)
    if (startTime) {
      const duration = performance.now() - startTime
      
      if (name === 'render') {
        renderTime.value = duration
        checkRenderPerformance(duration)
      }
      
      measurements.value.delete(name)
      return duration
    }
    return 0
  }
  
  // 检查内存使用情况
  const checkMemoryUsage = (): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usedMB = memory.usedJSHeapSize / 1024 / 1024
      memoryUsage.value = usedMB
      
      if (usedMB > config.maxMemoryUsage) {
        addWarning('memory', `内存使用过高: ${usedMB.toFixed(2)}MB`)
      } else {
        removeWarning('memory')
      }
      
      return usedMB
    }
    return 0
  }
  
  // 检查渲染性能
  const checkRenderPerformance = (duration: number) => {
    if (duration > config.maxRenderTime) {
      addWarning('render', `渲染时间过长: ${duration.toFixed(2)}ms`)
    } else {
      removeWarning('render')
    }
  }
  
  // 计算帧率
  const calculateFrameRate = () => {
    const now = performance.now()
    frameTimestamps.value.push(now)
    
    // 只保留最近1秒的时间戳
    const oneSecondAgo = now - 1000
    frameTimestamps.value = frameTimestamps.value.filter(timestamp => timestamp > oneSecondAgo)
    
    frameRate.value = frameTimestamps.value.length
    
    if (frameRate.value < config.minFrameRate) {
      addWarning('framerate', `帧率过低: ${frameRate.value}fps`)
    } else {
      removeWarning('framerate')
    }
  }
  
  // 跟踪更新频率
  const trackUpdate = () => {
    const now = performance.now()
    updateTimestamps.value.push(now)
    updateCount.value++
    
    // 只保留最近1秒的更新
    const oneSecondAgo = now - 1000
    updateTimestamps.value = updateTimestamps.value.filter(timestamp => timestamp > oneSecondAgo)
    
    const updateFrequency = updateTimestamps.value.length
    
    if (updateFrequency > config.maxUpdateFrequency) {
      addWarning('updates', `更新频率过高: ${updateFrequency}/sec`)
    } else {
      removeWarning('updates')
    }
  }
  
  // 添加警告
  const addWarning = (type: string, message: string) => {
    warnings.value.add(type)
    if (!performanceIssues.value.includes(message)) {
      performanceIssues.value.push(message)
    }
  }
  
  // 移除警告
  const removeWarning = (type: string) => {
    warnings.value.delete(type)
    performanceIssues.value = performanceIssues.value.filter(issue => !issue.includes(type))
  }
  
  // 获取性能报告
  const getPerformanceReport = (): PerformanceMetrics => {
    return {
      renderTime: renderTime.value,
      memoryUsage: memoryUsage.value,
      cpuUsage: cpuUsage.value,
      taskCount: 0, // 需要外部传入
      updateFrequency: updateTimestamps.value.length,
      frameRate: frameRate.value
    }
  }
  
  // 重置性能统计
  const resetStats = () => {
    renderTime.value = 0
    memoryUsage.value = 0
    cpuUsage.value = 0
    frameRate.value = 60
    updateCount.value = 0
    frameTimestamps.value = []
    updateTimestamps.value = []
    warnings.value.clear()
    performanceIssues.value = []
    measurements.value.clear()
  }
  
  // 性能优化建议
  const getOptimizationSuggestions = (): string[] => {
    const suggestions: string[] = []
    
    if (warnings.value.has('memory')) {
      suggestions.push('考虑减少同时显示的任务数量')
      suggestions.push('启用虚拟滚动以减少DOM元素')
      suggestions.push('清理已完成的任务以释放内存')
    }
    
    if (warnings.value.has('render')) {
      suggestions.push('减少状态更新频率')
      suggestions.push('使用防抖或节流优化更新')
      suggestions.push('考虑分页显示大量数据')
    }
    
    if (warnings.value.has('framerate')) {
      suggestions.push('减少复杂的CSS动画')
      suggestions.push('优化组件渲染逻辑')
      suggestions.push('使用Web Workers处理重计算')
    }
    
    if (warnings.value.has('updates')) {
      suggestions.push('合并频繁的状态更新')
      suggestions.push('使用批量更新机制')
      suggestions.push('减少不必要的响应式数据变更')
    }
    
    return suggestions
  }
  
  // 自动性能监控
  const startAutoMonitoring = (interval: number = 1000) => {
    const monitoringInterval = setInterval(() => {
      checkMemoryUsage()
      calculateFrameRate()
      trackUpdate()
    }, interval)
    
    onUnmounted(() => {
      clearInterval(monitoringInterval)
    })
    
    return () => clearInterval(monitoringInterval)
  }
  
  // 计算属性
  const hasPerformanceIssues = computed(() => warnings.value.size > 0)
  const performanceScore = computed(() => {
    let score = 100
    
    if (warnings.value.has('memory')) score -= 25
    if (warnings.value.has('render')) score -= 25
    if (warnings.value.has('framerate')) score -= 25
    if (warnings.value.has('updates')) score -= 25
    
    return Math.max(0, score)
  })
  
  const performanceLevel = computed(() => {
    const score = performanceScore.value
    if (score >= 90) return 'excellent'
    if (score >= 70) return 'good'
    if (score >= 50) return 'fair'
    return 'poor'
  })
  
  return {
    // 性能指标
    renderTime: computed(() => renderTime.value),
    memoryUsage: computed(() => memoryUsage.value),
    cpuUsage: computed(() => cpuUsage.value),
    frameRate: computed(() => frameRate.value),
    updateCount: computed(() => updateCount.value),
    
    // 警告和问题
    warnings: computed(() => Array.from(warnings.value)),
    performanceIssues: computed(() => performanceIssues.value),
    hasPerformanceIssues,
    performanceScore,
    performanceLevel,
    
    // 测量方法
    startMeasure,
    endMeasure,
    checkMemoryUsage,
    trackUpdate,
    
    // 报告和建议
    getPerformanceReport,
    getOptimizationSuggestions,
    
    // 控制方法
    resetStats,
    startAutoMonitoring
  }
}

// 防抖Hook
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): T {
  let timeoutId: NodeJS.Timeout
  
  return ((...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn(...args), delay)
  }) as T
}

// 节流Hook
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): T {
  let lastCall = 0
  
  return ((...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return fn(...args)
    }
  }) as T
}

// 批量更新Hook
export function useBatchUpdate<T>(
  updateFn: (items: T[]) => void,
  batchSize: number = 10,
  delay: number = 100
) {
  const pendingUpdates = ref<T[]>([])
  let timeoutId: NodeJS.Timeout
  
  const addUpdate = (item: T) => {
    pendingUpdates.value.push(item)
    
    // 如果达到批次大小，立即执行
    if (pendingUpdates.value.length >= batchSize) {
      flushUpdates()
    } else {
      // 否则延迟执行
      clearTimeout(timeoutId)
      timeoutId = setTimeout(flushUpdates, delay)
    }
  }
  
  const flushUpdates = () => {
    if (pendingUpdates.value.length > 0) {
      const updates = [...pendingUpdates.value]
      pendingUpdates.value = []
      updateFn(updates)
    }
    clearTimeout(timeoutId)
  }
  
  onUnmounted(() => {
    flushUpdates()
  })
  
  return {
    addUpdate,
    flushUpdates,
    pendingCount: computed(() => pendingUpdates.value.length)
  }
}
