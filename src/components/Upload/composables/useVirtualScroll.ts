import { ref, computed, watch, nextTick } from 'vue'

export interface VirtualScrollItem {
  id: string
  height?: number
  [key: string]: any
}

export interface VirtualScrollOptions {
  items: any
  containerHeight: number
  itemHeight: number
  bufferSize: number
}

export function useVirtualScroll(options: VirtualScrollOptions) {
  const scrollTop = ref(0)
  const itemHeights = ref<Map<string, number>>(new Map())
  
  // 获取项目高度
  const getItemHeight = (item: VirtualScrollItem): number => {
    return itemHeights.value.get(item.id) || options.itemHeight
  }
  
  // 计算总高度
  const totalHeight = computed(() => {
    return options.items.value.reduce((total: number, item: VirtualScrollItem) => {
      return total + getItemHeight(item)
    }, 0)
  })
  
  // 计算可见区域高度
  const visibleHeight = computed(() => options.containerHeight)
  
  // 计算开始索引
  const startIndex = computed(() => {
    let accumulatedHeight = 0
    let index = 0
    
    for (const item of options.items.value) {
      const itemHeight = getItemHeight(item)
      if (accumulatedHeight + itemHeight > scrollTop.value) {
        break
      }
      accumulatedHeight += itemHeight
      index++
    }
    
    return Math.max(0, index - options.bufferSize)
  })
  
  // 计算结束索引
  const endIndex = computed(() => {
    let accumulatedHeight = 0
    let index = 0
    const targetHeight = scrollTop.value + visibleHeight.value
    
    for (const item of options.items.value) {
      const itemHeight = getItemHeight(item)
      accumulatedHeight += itemHeight
      index++
      
      if (accumulatedHeight >= targetHeight) {
        break
      }
    }
    
    return Math.min(options.items.value.length, index + options.bufferSize)
  })
  
  // 计算偏移量
  const offsetY = computed(() => {
    let offset = 0
    for (let i = 0; i < startIndex.value; i++) {
      if (i < options.items.value.length) {
        offset += getItemHeight(options.items.value[i])
      }
    }
    return offset
  })
  
  // 可见项目
  const visibleItems = computed(() => {
    return options.items.value.slice(startIndex.value, endIndex.value)
  })
  
  // 滚动处理
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }
  
  // 滚动到指定项目
  const scrollToItem = (itemId: string) => {
    const index = options.items.value.findIndex((item: VirtualScrollItem) => item.id === itemId)
    if (index === -1) return
    
    let offset = 0
    for (let i = 0; i < index; i++) {
      offset += getItemHeight(options.items.value[i])
    }
    
    const container = document.querySelector('.virtual-scroll-container') as HTMLElement
    if (container) {
      container.scrollTop = offset
    }
  }
  
  // 更新项目高度
  const updateItemHeight = (itemId: string, height: number) => {
    itemHeights.value.set(itemId, height)
  }
  
  // 重置滚动位置
  const resetScroll = () => {
    scrollTop.value = 0
    const container = document.querySelector('.virtual-scroll-container') as HTMLElement
    if (container) {
      container.scrollTop = 0
    }
  }
  
  // 获取滚动统计信息
  const getScrollStats = () => {
    return {
      totalItems: options.items.value.length,
      visibleItems: visibleItems.value.length,
      startIndex: startIndex.value,
      endIndex: endIndex.value,
      scrollTop: scrollTop.value,
      totalHeight: totalHeight.value
    }
  }
  
  return {
    visibleItems,
    offsetY,
    totalHeight,
    visibleHeight,
    startIndex,
    endIndex,
    handleScroll,
    scrollToItem,
    updateItemHeight,
    resetScroll,
    getScrollStats
  }
}

// 虚拟滚动性能优化Hook
export function useVirtualScrollOptimization() {
  const renderCount = ref(0)
  const lastRenderTime = ref(0)
  const averageRenderTime = ref(0)
  
  const trackRender = () => {
    const now = performance.now()
    if (lastRenderTime.value > 0) {
      const renderTime = now - lastRenderTime.value
      renderCount.value++
      averageRenderTime.value = (averageRenderTime.value * (renderCount.value - 1) + renderTime) / renderCount.value
    }
    lastRenderTime.value = now
  }
  
  const resetStats = () => {
    renderCount.value = 0
    lastRenderTime.value = 0
    averageRenderTime.value = 0
  }
  
  return {
    renderCount: computed(() => renderCount.value),
    averageRenderTime: computed(() => averageRenderTime.value),
    trackRender,
    resetStats
  }
}

// 懒加载Hook
export function useLazyLoading<T>(
  items: any,
  pageSize: number = 50,
  loadMore?: () => Promise<T[]>
) {
  const loadedItems = ref<T[]>([])
  const loading = ref(false)
  const hasMore = ref(true)
  const currentPage = ref(0)
  
  // 初始化加载
  const initializeItems = () => {
    const initialItems = items.value.slice(0, pageSize)
    loadedItems.value = initialItems
    currentPage.value = 1
    hasMore.value = items.value.length > pageSize
  }
  
  // 加载更多项目
  const loadMoreItems = async () => {
    if (loading.value || !hasMore.value) return
    
    loading.value = true
    
    try {
      if (loadMore) {
        // 使用自定义加载函数
        const newItems = await loadMore()
        loadedItems.value.push(...newItems)
        hasMore.value = newItems.length === pageSize
      } else {
        // 从现有数据中分页加载
        const startIndex = currentPage.value * pageSize
        const endIndex = startIndex + pageSize
        const newItems = items.value.slice(startIndex, endIndex)
        
        loadedItems.value.push(...newItems)
        currentPage.value++
        hasMore.value = endIndex < items.value.length
      }
    } catch (error) {
      console.error('加载更多项目失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 重置加载状态
  const resetLoading = () => {
    loadedItems.value = []
    currentPage.value = 0
    hasMore.value = true
    loading.value = false
  }
  
  // 监听原始数据变化
  watch(items, initializeItems, { immediate: true })
  
  return {
    loadedItems: computed(() => loadedItems.value),
    loading: computed(() => loading.value),
    hasMore: computed(() => hasMore.value),
    loadMoreItems,
    resetLoading
  }
}

// 滚动到底部检测Hook
export function useScrollToBottom(
  threshold: number = 100,
  onScrollToBottom?: () => void
) {
  const isNearBottom = ref(false)
  
  const checkScrollPosition = (event: Event) => {
    const target = event.target as HTMLElement
    const { scrollTop, scrollHeight, clientHeight } = target
    
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight
    const nearBottom = distanceFromBottom <= threshold
    
    if (nearBottom && !isNearBottom.value) {
      isNearBottom.value = true
      onScrollToBottom?.()
    } else if (!nearBottom) {
      isNearBottom.value = false
    }
  }
  
  return {
    isNearBottom: computed(() => isNearBottom.value),
    checkScrollPosition
  }
}

// 平滑滚动Hook
export function useSmoothScroll() {
  const scrollToPosition = (
    container: HTMLElement,
    targetPosition: number,
    duration: number = 300
  ) => {
    const startPosition = container.scrollTop
    const distance = targetPosition - startPosition
    const startTime = performance.now()
    
    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // 使用缓动函数
      const easeInOutCubic = (t: number) => 
        t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
      
      const easedProgress = easeInOutCubic(progress)
      container.scrollTop = startPosition + distance * easedProgress
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll)
      }
    }
    
    requestAnimationFrame(animateScroll)
  }
  
  return {
    scrollToPosition
  }
}
