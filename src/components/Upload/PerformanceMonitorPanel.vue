<template>
  <div class="performance-monitor-panel">
    <div class="panel-header">
      <h3>性能监控面板</h3>
      <div class="panel-controls">
        <button 
          @click="toggleAutoRefresh"
          :class="['control-btn', { active: autoRefresh }]"
        >
          {{ autoRefresh ? '停止刷新' : '自动刷新' }}
        </button>
        <button @click="refreshStats" class="control-btn">
          手动刷新
        </button>
        <button @click="clearStats" class="control-btn danger">
          清理内存
        </button>
      </div>
    </div>

    <!-- 性能概览 -->
    <div class="performance-overview">
      <div class="metric-card">
        <div class="metric-title">内存使用</div>
        <div class="metric-value" :class="getMemoryStatusClass()">
          {{ stats.memoryUsage.toFixed(2) }} MB
        </div>
        <div class="metric-subtitle">
          {{ stats.activeFiles }} 个活跃文件
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-title">渲染性能</div>
        <div class="metric-value" :class="getRenderStatusClass()">
          {{ stats.renderTime.toFixed(2) }} ms
        </div>
        <div class="metric-subtitle">
          {{ stats.frameRate }} fps
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-title">任务数量</div>
        <div class="metric-value">
          {{ stats.taskCount }}
        </div>
        <div class="metric-subtitle">
          显示: {{ stats.displayTaskCount }}
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-title">更新频率</div>
        <div class="metric-value" :class="getUpdateStatusClass()">
          {{ stats.updateFrequency }}/s
        </div>
        <div class="metric-subtitle">
          命中率: {{ memoryStats.hitRate }}%
        </div>
      </div>
    </div>

    <!-- 详细统计 -->
    <div class="detailed-stats">
      <div class="stats-section">
        <h4>内存统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span>文件池大小:</span>
            <span>{{ memoryStats.poolSize }}</span>
          </div>
          <div class="stat-item">
            <span>文件池使用:</span>
            <span>{{ memoryStats.filePoolUsage.toFixed(2) }} MB</span>
          </div>
          <div class="stat-item">
            <span>活跃文件:</span>
            <span>{{ memoryStats.activeFiles }}</span>
          </div>
          <div class="stat-item">
            <span>缓存命中率:</span>
            <span>{{ memoryStats.hitRate.toFixed(1) }}%</span>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <h4>IPC队列统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span>总消息数:</span>
            <span>{{ ipcStats.total }}</span>
          </div>
          <div class="stat-item">
            <span>高优先级:</span>
            <span>{{ ipcStats.byPriority[0] || 0 }}</span>
          </div>
          <div class="stat-item">
            <span>中优先级:</span>
            <span>{{ ipcStats.byPriority[1] || 0 }}</span>
          </div>
          <div class="stat-item">
            <span>低优先级:</span>
            <span>{{ ipcStats.byPriority[2] || 0 }}</span>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <h4>批次管理统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span>批次管理器:</span>
            <span>{{ stats.batchManagerCount }}</span>
          </div>
          <div class="stat-item">
            <span>批量任务:</span>
            <span>{{ stats.batchTaskCount }}</span>
          </div>
          <div class="stat-item">
            <span>独立任务:</span>
            <span>{{ stats.taskCount - stats.batchTaskCount }}</span>
          </div>
          <div class="stat-item">
            <span>显示任务:</span>
            <span>{{ stats.displayTaskCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能建议 -->
    <div v-if="suggestions.length > 0" class="performance-suggestions">
      <h4>性能优化建议</h4>
      <ul class="suggestions-list">
        <li v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
          {{ suggestion }}
        </li>
      </ul>
    </div>

    <!-- 性能图表 -->
    <div class="performance-charts">
      <div class="chart-container">
        <h4>内存使用趋势</h4>
        <div class="simple-chart">
          <div 
            v-for="(point, index) in memoryHistory" 
            :key="index"
            class="chart-bar"
            :style="{ 
              height: (point / maxMemory * 100) + '%',
              backgroundColor: point > 200 ? '#f44336' : point > 100 ? '#ff9800' : '#4caf50'
            }"
          ></div>
        </div>
      </div>

      <div class="chart-container">
        <h4>渲染时间趋势</h4>
        <div class="simple-chart">
          <div 
            v-for="(point, index) in renderHistory" 
            :key="index"
            class="chart-bar"
            :style="{ 
              height: (point / 50 * 100) + '%',
              backgroundColor: point > 16 ? '#f44336' : point > 10 ? '#ff9800' : '#4caf50'
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTusUpload } from './composables/useTusUpload'

const tusUpload = useTusUpload()

// 状态
const autoRefresh = ref(true)
const refreshInterval = ref<NodeJS.Timeout>()
const memoryHistory = ref<number[]>([])
const renderHistory = ref<number[]>([])
const maxHistoryLength = 50

// 统计数据
const stats = ref({
  memoryUsage: 0,
  renderTime: 0,
  frameRate: 60,
  taskCount: 0,
  batchTaskCount: 0,
  batchManagerCount: 0,
  displayTaskCount: 0,
  updateFrequency: 0,
  suggestions: [] as string[]
})

const memoryStats = ref({
  totalMemoryUsage: 0,
  filePoolUsage: 0,
  activeFiles: 0,
  poolSize: 0,
  hitRate: 0
})

const ipcStats = ref({
  total: 0,
  byPriority: {} as Record<number, number>,
  maxCapacity: 1000
})

// 计算属性
const suggestions = computed(() => stats.value.suggestions)
const maxMemory = computed(() => Math.max(...memoryHistory.value, 100))

// 方法
const refreshStats = () => {
  const performanceStats = tusUpload.getPerformanceStats()
  
  stats.value = {
    memoryUsage: performanceStats.memoryUsage,
    renderTime: performanceStats.renderTime,
    frameRate: performanceStats.frameRate,
    taskCount: performanceStats.taskCount,
    batchTaskCount: performanceStats.batchTaskCount,
    batchManagerCount: performanceStats.batchManagerCount,
    displayTaskCount: performanceStats.displayTaskCount,
    updateFrequency: performanceStats.updateFrequency,
    suggestions: performanceStats.suggestions
  }

  // 更新历史数据
  memoryHistory.value.push(performanceStats.memoryUsage)
  if (memoryHistory.value.length > maxHistoryLength) {
    memoryHistory.value.shift()
  }

  renderHistory.value.push(performanceStats.renderTime)
  if (renderHistory.value.length > maxHistoryLength) {
    renderHistory.value.shift()
  }

  // 模拟获取内存和IPC统计（实际应该从Electron端获取）
  memoryStats.value = {
    totalMemoryUsage: performanceStats.memoryUsage,
    filePoolUsage: performanceStats.memoryUsage * 0.7,
    activeFiles: Math.floor(performanceStats.taskCount * 0.3),
    poolSize: Math.floor(performanceStats.taskCount * 0.2),
    hitRate: 85 + Math.random() * 10
  }

  ipcStats.value = {
    total: Math.floor(Math.random() * 100),
    byPriority: {
      0: Math.floor(Math.random() * 10),
      1: Math.floor(Math.random() * 50),
      2: Math.floor(Math.random() * 40)
    },
    maxCapacity: 1000
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  refreshInterval.value = setInterval(refreshStats, 1000)
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = undefined
  }
}

const clearStats = () => {
  tusUpload.cleanupMemory()
  memoryHistory.value = []
  renderHistory.value = []
  refreshStats()
}

// 状态样式类
const getMemoryStatusClass = () => {
  const usage = stats.value.memoryUsage
  if (usage > 200) return 'status-danger'
  if (usage > 100) return 'status-warning'
  return 'status-good'
}

const getRenderStatusClass = () => {
  const time = stats.value.renderTime
  if (time > 16) return 'status-danger'
  if (time > 10) return 'status-warning'
  return 'status-good'
}

const getUpdateStatusClass = () => {
  const freq = stats.value.updateFrequency
  if (freq > 60) return 'status-danger'
  if (freq > 30) return 'status-warning'
  return 'status-good'
}

// 生命周期
onMounted(() => {
  refreshStats()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.performance-monitor-panel {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #ddd;
}

.panel-controls {
  display: flex;
  gap: 10px;
}

.control-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.control-btn:hover {
  background: #f0f0f0;
}

.control-btn.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.control-btn.danger {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.performance-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.metric-card {
  background: white;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.metric-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.metric-value.status-good { color: #4caf50; }
.metric-value.status-warning { color: #ff9800; }
.metric-value.status-danger { color: #f44336; }

.metric-subtitle {
  font-size: 11px;
  color: #999;
}

.detailed-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-section {
  background: white;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  padding: 4px 0;
}

.performance-suggestions {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.suggestions-list {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.suggestion-item {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.performance-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  background: white;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.simple-chart {
  display: flex;
  align-items: end;
  height: 100px;
  gap: 2px;
}

.chart-bar {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: all 0.3s ease;
}
</style>
