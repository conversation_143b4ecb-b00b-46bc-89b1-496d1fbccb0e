<template>
  <div class="optimization-demo p-6 max-w-4xl mx-auto">
    <div class="demo-header mb-6">
      <h2 class="text-2xl font-bold mb-2">GlobalProgressIndicator 优化演示</h2>
      <p class="text-muted-foreground">
        演示任务数量修复和虚拟滚动性能优化效果
      </p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel bg-card p-4 rounded-lg border mb-6">
      <h3 class="text-lg font-semibold mb-4">测试控制</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="control-group">
          <label class="block text-sm font-medium mb-2">文件数量</label>
          <select v-model="selectedFileCount" class="w-full p-2 border rounded">
            <option value="10">10个文件</option>
            <option value="50">50个文件</option>
            <option value="100">100个文件</option>
            <option value="200">200个文件</option>
            <option value="500">500个文件</option>
          </select>
        </div>
        
        <div class="control-group">
          <label class="block text-sm font-medium mb-2">上传类型</label>
          <select v-model="selectedUploadType" class="w-full p-2 border rounded">
            <option value="folder">文件夹上传</option>
            <option value="batch">分批上传</option>
            <option value="individual">独立上传</option>
          </select>
        </div>
        
        <div class="control-group">
          <label class="block text-sm font-medium mb-2">虚拟滚动</label>
          <select v-model="virtualScrollEnabled" class="w-full p-2 border rounded">
            <option :value="true">启用</option>
            <option :value="false">禁用</option>
          </select>
        </div>
      </div>
      
      <div class="action-buttons flex gap-3">
        <button 
          @click="startDemo"
          :disabled="demoRunning"
          class="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50"
        >
          {{ demoRunning ? '运行中...' : '开始演示' }}
        </button>
        
        <button 
          @click="clearTasks"
          class="px-4 py-2 bg-secondary text-secondary-foreground rounded hover:bg-secondary/90"
        >
          清理任务
        </button>
        
        <button 
          @click="runVerification"
          :disabled="verificationRunning"
          class="px-4 py-2 bg-accent text-accent-foreground rounded hover:bg-accent/90 disabled:opacity-50"
        >
          {{ verificationRunning ? '验证中...' : '运行验证测试' }}
        </button>
      </div>
    </div>

    <!-- 统计面板 -->
    <div class="stats-panel bg-card p-4 rounded-lg border mb-6">
      <h3 class="text-lg font-semibold mb-4">实时统计</h3>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-primary">{{ stats.totalFiles }}</div>
          <div class="text-sm text-muted-foreground">总文件数</div>
        </div>
        
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-blue-600">{{ stats.displayedTasks }}</div>
          <div class="text-sm text-muted-foreground">显示任务数</div>
        </div>
        
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-green-600">{{ stats.batchTasks }}</div>
          <div class="text-sm text-muted-foreground">批量任务数</div>
        </div>
        
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-purple-600">{{ stats.renderTime }}ms</div>
          <div class="text-sm text-muted-foreground">渲染时间</div>
        </div>
      </div>
      
      <div class="mt-4 p-3 bg-muted rounded">
        <div class="text-sm">
          <span class="font-medium">任务计数准确性:</span>
          <span :class="stats.countAccurate ? 'text-green-600' : 'text-red-600'">
            {{ stats.countAccurate ? '✅ 正确' : '❌ 错误' }}
          </span>
        </div>
        <div class="text-sm mt-1">
          <span class="font-medium">性能状态:</span>
          <span :class="getPerformanceStatusClass()">
            {{ getPerformanceStatusText() }}
          </span>
        </div>
      </div>
    </div>

    <!-- 进度指示器 -->
    <div class="progress-indicator mb-6">
      <GlobalProgressIndicator />
    </div>

    <!-- 日志面板 -->
    <div class="log-panel bg-card p-4 rounded-lg border">
      <h3 class="text-lg font-semibold mb-4">操作日志</h3>
      
      <div class="log-content h-32 overflow-y-auto bg-muted p-3 rounded text-sm font-mono">
        <div v-for="(log, index) in logs" :key="index" class="log-entry mb-1">
          <span class="text-muted-foreground">{{ formatTime(log.timestamp) }}</span>
          <span :class="getLogLevelClass(log.level)">{{ log.message }}</span>
        </div>
        
        <div v-if="logs.length === 0" class="text-muted-foreground">
          暂无日志，点击"开始演示"查看效果
        </div>
      </div>
      
      <div class="log-actions mt-3 flex gap-2">
        <button 
          @click="clearLogs"
          class="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded hover:bg-secondary/90"
        >
          清理日志
        </button>
        
        <button 
          @click="exportLogs"
          class="px-3 py-1 text-sm bg-accent text-accent-foreground rounded hover:bg-accent/90"
        >
          导出日志
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import { useGlobalProgress } from '@/composables/useGlobalProgress'
import { runTaskCountFixVerification } from './test-task-count-fix'
import GlobalProgressIndicator from './index.vue'

// 状态
const selectedFileCount = ref(50)
const selectedUploadType = ref('folder')
const virtualScrollEnabled = ref(true)
const demoRunning = ref(false)
const verificationRunning = ref(false)

// 日志
interface LogEntry {
  timestamp: Date
  level: 'info' | 'success' | 'warning' | 'error'
  message: string
}

const logs = ref<LogEntry[]>([])

// 统计
const stats = ref({
  totalFiles: 0,
  displayedTasks: 0,
  batchTasks: 0,
  renderTime: 0,
  countAccurate: true
})

// 组合式API
const tusUpload = useTusUpload()
const globalProgress = useGlobalProgress()

// 计算属性
const performanceStats = computed(() => tusUpload.getPerformanceStats())

// 方法
const addLog = (level: LogEntry['level'], message: string) => {
  logs.value.push({
    timestamp: new Date(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.shift()
  }
}

const createMockFile = (name: string, size: number = 1024): File => {
  const content = new Array(size).fill('a').join('')
  const blob = new Blob([content], { type: 'text/plain' })
  return new File([blob], name, { type: 'text/plain' })
}

const createMockFiles = (count: number, prefix: string): File[] => {
  const files: File[] = []
  for (let i = 1; i <= count; i++) {
    const fileName = `${prefix}/file-${i.toString().padStart(3, '0')}.txt`
    const fileSize = Math.floor(Math.random() * 2000) + 500
    files.push(createMockFile(fileName, fileSize))
  }
  return files
}

const startDemo = async () => {
  if (demoRunning.value) return
  
  demoRunning.value = true
  addLog('info', `开始演示: ${selectedFileCount.value}个文件, ${selectedUploadType.value}模式`)
  
  try {
    // 应用性能配置
    tusUpload.updatePerformanceConfig({
      enableVirtualScroll: virtualScrollEnabled.value,
      maxVisibleTasks: 100,
      updateThrottleMs: 100,
      batchUpdateSize: 10,
      enableLazyLoading: true
    })
    
    addLog('success', `虚拟滚动: ${virtualScrollEnabled.value ? '启用' : '禁用'}`)
    
    // 创建测试文件
    const files = createMockFiles(selectedFileCount.value, 'demo-folder')
    stats.value.totalFiles = files.length
    
    addLog('info', `创建了 ${files.length} 个测试文件`)
    
    // 根据类型执行上传
    if (selectedUploadType.value === 'individual') {
      // 逐个上传
      for (let i = 0; i < files.length; i++) {
        await tusUpload.uploadFiles([files[i]], {
          category: 'demo',
          source: 'optimization-demo-individual'
        })
        
        if (i % 10 === 0) {
          addLog('info', `已创建 ${i + 1} 个独立任务`)
        }
      }
    } else {
      // 批量上传
      await tusUpload.uploadFiles(files, {
        category: 'demo',
        source: 'optimization-demo-batch',
        demoType: selectedUploadType.value
      })
    }
    
    addLog('success', '演示任务创建完成')
    
  } catch (error) {
    addLog('error', `演示失败: ${error instanceof Error ? error.message : String(error)}`)
  } finally {
    demoRunning.value = false
  }
}

const clearTasks = () => {
  tusUpload.cleanupMemory()
  globalProgress.clearAllTasks()
  stats.value = {
    totalFiles: 0,
    displayedTasks: 0,
    batchTasks: 0,
    renderTime: 0,
    countAccurate: true
  }
  addLog('info', '已清理所有任务')
}

const runVerification = async () => {
  if (verificationRunning.value) return
  
  verificationRunning.value = true
  addLog('info', '开始运行验证测试...')
  
  try {
    await runTaskCountFixVerification()
    addLog('success', '验证测试完成，请查看控制台输出')
  } catch (error) {
    addLog('error', `验证测试失败: ${error instanceof Error ? error.message : String(error)}`)
  } finally {
    verificationRunning.value = false
  }
}

const clearLogs = () => {
  logs.value = []
}

const exportLogs = () => {
  const logText = logs.value.map(log => 
    `[${formatTime(log.timestamp)}] ${log.level.toUpperCase()}: ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `optimization-demo-logs-${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString()
}

const getLogLevelClass = (level: LogEntry['level']): string => {
  const classes = {
    info: 'text-blue-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600'
  }
  return classes[level]
}

const getPerformanceStatusClass = (): string => {
  const renderTime = performanceStats.value.renderTime
  if (renderTime > 16) return 'text-red-600'
  if (renderTime > 10) return 'text-yellow-600'
  return 'text-green-600'
}

const getPerformanceStatusText = (): string => {
  const renderTime = performanceStats.value.renderTime
  if (renderTime > 16) return '❌ 性能较差'
  if (renderTime > 10) return '⚠️ 性能一般'
  return '✅ 性能良好'
}

// 定期更新统计
let statsInterval: NodeJS.Timeout

onMounted(() => {
  addLog('info', '优化演示组件已加载')
  
  statsInterval = setInterval(() => {
    const currentStats = performanceStats.value
    
    stats.value.displayedTasks = globalProgress.activeTasks.value.length
    stats.value.batchTasks = currentStats.batchTaskCount + currentStats.batchManagerCount
    stats.value.renderTime = currentStats.renderTime
    
    // 检查任务计数准确性
    if (selectedUploadType.value === 'individual') {
      stats.value.countAccurate = stats.value.displayedTasks === stats.value.totalFiles
    } else {
      stats.value.countAccurate = stats.value.batchTasks > 0 && stats.value.displayedTasks <= stats.value.batchTasks + 5
    }
  }, 1000)
})

onUnmounted(() => {
  if (statsInterval) {
    clearInterval(statsInterval)
  }
})
</script>

<style scoped>
.optimization-demo {
  font-family: system-ui, -apple-system, sans-serif;
}

.control-group label {
  font-weight: 500;
}

.stat-item {
  padding: 1rem;
  border-radius: 0.5rem;
  background: rgba(0, 0, 0, 0.02);
}

.log-entry {
  word-break: break-all;
}

.log-content {
  scrollbar-width: thin;
}

.log-content::-webkit-scrollbar {
  width: 6px;
}

.log-content::-webkit-scrollbar-track {
  background: transparent;
}

.log-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
</style>
