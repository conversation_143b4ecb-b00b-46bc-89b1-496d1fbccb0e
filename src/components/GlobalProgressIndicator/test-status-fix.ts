/**
 * 状态修复测试工具
 * 用于验证任务状态显示和重复任务清理功能
 */

import { useGlobalProgress } from '@/composables/useGlobalProgress'
import type { ProgressTask } from '@/composables/useGlobalProgress'

/**
 * 测试状态映射功能
 */
export function testStatusMapping() {
  console.log('🧪 测试状态映射功能...')
  
  const { mapTusStatusToProgressStatus } = useGlobalProgress() as any
  
  // 测试各种状态映射
  const testCases = [
    { input: 'pending', expected: 'pending' },
    { input: 'queued', expected: 'pending' },
    { input: 'uploading', expected: 'in-progress' },
    { input: 'paused', expected: 'paused' },
    { input: 'completed', expected: 'completed' },
    { input: 'error', expected: 'error' },
    { input: 'cancelled', expected: 'cancelled' },
    { input: 'unknown', expected: 'pending' } // 默认值测试
  ]
  
  testCases.forEach(({ input, expected }) => {
    const result = mapTusStatusToProgressStatus(input)
    const status = result === expected ? '✅' : '❌'
    console.log(`${status} ${input} -> ${result} (期望: ${expected})`)
  })
}

/**
 * 创建测试任务数据
 */
export function createTestTasks(): ProgressTask[] {
  return [
    {
      id: 'test-1',
      type: 'upload',
      fileName: 'test1.jpg',
      size: '1.2 MB',
      progress: 0,
      status: 'pending',
      startTime: new Date(),
      tusTaskId: 'tus-1'
    },
    {
      id: 'test-2',
      type: 'upload',
      fileName: 'test1.jpg', // 重复文件名
      size: '1.2 MB',
      progress: 50,
      status: 'in-progress',
      startTime: new Date(Date.now() - 1000),
      tusTaskId: 'tus-2'
    },
    {
      id: 'test-3',
      type: 'upload',
      fileName: 'test2.jpg',
      size: '2.5 MB',
      progress: 0,
      status: 'pending',
      startTime: new Date(),
      tusTaskId: 'tus-3'
    },
    {
      id: 'test-4',
      type: 'upload',
      fileName: 'test2.jpg', // 重复文件名
      size: '2.5 MB',
      progress: 0,
      status: 'pending',
      startTime: new Date(Date.now() - 2000),
      tusTaskId: 'tus-4'
    }
  ]
}

/**
 * 测试重复任务清理功能
 */
export function testDuplicateCleanup() {
  console.log('🧪 测试重复任务清理功能...')
  
  const { tasks, cleanupDuplicateTasks } = useGlobalProgress() as any
  
  // 保存原始任务
  const originalTasks = [...tasks.value]
  
  // 添加测试任务
  const testTasks = createTestTasks()
  tasks.value.push(...testTasks)
  
  console.log(`添加测试任务前: ${originalTasks.length} 个任务`)
  console.log(`添加测试任务后: ${tasks.value.length} 个任务`)
  
  // 执行清理
  cleanupDuplicateTasks()
  
  console.log(`清理后: ${tasks.value.length} 个任务`)
  
  // 检查是否正确清理了重复任务
  const fileNames = tasks.value.map(task => task.fileName)
  const uniqueFileNames = [...new Set(fileNames)]
  
  if (fileNames.length === uniqueFileNames.length) {
    console.log('✅ 重复任务清理成功')
  } else {
    console.log('❌ 仍有重复任务存在')
  }
  
  // 恢复原始任务
  tasks.value.splice(0, tasks.value.length, ...originalTasks)
}

/**
 * 测试状态显示文本
 */
export function testStatusDisplay() {
  console.log('🧪 测试状态显示文本...')
  
  const statusTests = [
    { status: 'pending', expectedText: '排队中', expectedColor: 'text-blue-500' },
    { status: 'in-progress', expectedText: '进度百分比', expectedColor: 'text-primary' },
    { status: 'paused', expectedText: '已暂停', expectedColor: 'text-yellow-500' },
    { status: 'error', expectedText: '失败', expectedColor: 'text-red-500' },
    { status: 'completed', expectedText: '已完成', expectedColor: 'text-green-600' }
  ]
  
  statusTests.forEach(({ status, expectedText, expectedColor }) => {
    console.log(`📋 ${status}: ${expectedText} (${expectedColor})`)
  })
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行状态修复测试...')
  console.log('=' * 50)
  
  testStatusMapping()
  console.log('')
  
  testDuplicateCleanup()
  console.log('')
  
  testStatusDisplay()
  console.log('')
  
  console.log('✅ 所有测试完成')
}

// 在开发环境中自动运行测试
if (import.meta.env.DEV) {
  // 延迟运行，确保组件初始化完成
  setTimeout(() => {
    if (typeof window !== 'undefined' && (window as any).__runStatusTests) {
      runAllTests()
    }
  }, 2000)
}
