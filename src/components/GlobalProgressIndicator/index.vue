<template>
  <div class="fixed right-4 bottom-24 z-50">
    <div class="flex flex-col gap-2 items-end">
      <!-- 进度面板 -->
      <ProgressPanel v-if="showPanel" :active-tasks="activeTasks" :upload-tasks="uploadTasks" :error-tasks="errorTasks"
        :has-active-tasks="hasActiveTasks" :overall-progress="overallProgress" :minimized="minimized"
        :upload-history="uploadHistory" :download-history="downloadHistory" :all-history="allHistory"
        @toggle-minimized="minimized = !minimized" @remove-task="removeTask" @retry-task="retryTask"
        @clear-error-tasks="clearErrorTasks" @clear-all-tasks="clearAllTasks"
        @clear-upload-history="handleClearUploadHistory" @clear-download-history="handleClearDownloadHistory"
        @clear-history="handleClearHistory" @remove-history-item="removeHistoryItem"
        @retry-history-task="retryHistoryTask" @pause-task="handlePauseTask" @resume-task="handleResumeTask"
        @cancel-task="handleCancelTask" />

      <!-- 悬浮图标按钮 -->
      <ProgressFloatButton :active-tasks="activeTasks" :upload-tasks="uploadTasks" :error-tasks="errorTasks"
        :has-active-tasks="hasActiveTasks" :overall-progress="overallProgress" @toggle="togglePanel" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useGlobalProgress } from '@/composables/useGlobalProgress'
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import { toast } from 'vue-sonner'
import ProgressFloatButton from './ProgressFloatButton.vue'
import ProgressPanel from './ProgressPanel.vue'

const {
  activeTasks,
  uploadTasks,
  overallProgress,
  hasActiveTasks,
  recentCompletedTasks,
  recentCompletedBatchTasks,
  errorTasks,
  uploadHistory,
  downloadHistory,
  allHistory,
  cancelTask,
  removeTask,
  deleteTask,
  clearAllTasks,
  retryTask,
  clearUploadHistory,
  clearDownloadHistory,
  clearHistory,
  removeHistoryItem,
  pauseTask,
  resumeTask
} = useGlobalProgress()

const tusUpload = useTusUpload()

// 应用性能优化配置
tusUpload.updatePerformanceConfig({
  enableVirtualScroll: true,
  maxVisibleTasks: 100,
  updateThrottleMs: 100,
  batchUpdateSize: 10,
  memoryCleanupInterval: 30000,
  enableLazyLoading: true,
  progressUpdateInterval: 500
})

const minimized = ref(false)
const showPanel = ref(false)

// LocalStorage 键名
const NOTIFIED_TASKS_KEY = 'clouddrive-notified-tasks'
const NOTIFIED_BATCH_TASKS_KEY = 'clouddrive-notified-batch-tasks'

// 从 localStorage 加载已通知的任务状态
const loadNotifiedTasks = (): Set<string> => {
  try {
    const stored = localStorage.getItem(NOTIFIED_TASKS_KEY)
    return stored ? new Set(JSON.parse(stored)) : new Set()
  } catch {
    return new Set()
  }
}

const loadNotifiedBatchTasks = (): Set<string> => {
  try {
    const stored = localStorage.getItem(NOTIFIED_BATCH_TASKS_KEY)
    return stored ? new Set(JSON.parse(stored)) : new Set()
  } catch {
    return new Set()
  }
}

// 保存已通知的任务状态到 localStorage
const saveNotifiedTasks = (tasks: Set<string>) => {
  try {
    localStorage.setItem(NOTIFIED_TASKS_KEY, JSON.stringify([...tasks]))
  } catch {
    // 忽略保存错误
  }
}

const saveNotifiedBatchTasks = (tasks: Set<string>) => {
  try {
    localStorage.setItem(NOTIFIED_BATCH_TASKS_KEY, JSON.stringify([...tasks]))
  } catch {
    // 忽略保存错误
  }
}

// 用于跟踪已经显示过通知的任务ID
const notifiedTaskIds = ref<Set<string>>(new Set())
const notifiedBatchTaskIds = ref<Set<string>>(new Set())

// 初始化时加载持久化的状态
onMounted(() => {
  notifiedTaskIds.value = loadNotifiedTasks()
  notifiedBatchTaskIds.value = loadNotifiedBatchTasks()
})

// 监听完成的任务，显示 sonner 通知
watch(recentCompletedTasks, (newTasks) => {
  // 过滤掉属于批量任务的子任务，只保留独立的单文件任务
  const standaloneCompletedTasks = newTasks.filter(task => {
    // 通过 tusTaskId 查找对应的 TUS 任务
    if (task.tusTaskId) {
      const tusTask = Array.from(tusUpload.tasks.value.values())
        .find(t => t.id === task.tusTaskId)

      // 如果是子任务，则不显示通知
      if (tusTask?.isSubTask) {
        return false
      }
    }
    return true
  })

  standaloneCompletedTasks.forEach(task => {
    if (!notifiedTaskIds.value.has(task.id)) {
      notifiedTaskIds.value.add(task.id)
      saveNotifiedTasks(notifiedTaskIds.value)
    }
  })
}, { immediate: false })

// 监听完成的批量任务，显示 sonner 通知  
watch(recentCompletedBatchTasks, (newBatchTasks) => {
  newBatchTasks.forEach(batchTask => {
    if (!notifiedBatchTaskIds.value.has(batchTask.id)) {
      notifiedBatchTaskIds.value.add(batchTask.id)
      saveNotifiedBatchTasks(notifiedBatchTaskIds.value)

      const totalFiles = batchTask.totalFiles || 0
      const completedFiles = batchTask.completedFiles || 0
      const failedFiles = (batchTask.failedFiles || 0)

      let description = `${batchTask.batchName}`

      if (failedFiles > 0) {
        description += ` (成功 ${completedFiles}，失败 ${failedFiles}，共 ${totalFiles} 个文件)`
      } else {
        description += ` (${completedFiles}/${totalFiles} 个文件)`
      }
    }
  })
}, { immediate: false })

// 清理已通知的任务ID（当任务不再存在时）
watch([recentCompletedTasks, recentCompletedBatchTasks], ([tasks, batchTasks]) => {
  const taskIds = new Set(tasks.map(t => t.id))
  const batchTaskIds = new Set(batchTasks.map(t => t.id))

  let needSave = false
  let needBatchSave = false

  // 清理不存在的任务ID
  notifiedTaskIds.value.forEach(id => {
    if (!taskIds.has(id)) {
      notifiedTaskIds.value.delete(id)
      needSave = true
    }
  })

  notifiedBatchTaskIds.value.forEach(id => {
    if (!batchTaskIds.has(id)) {
      notifiedBatchTaskIds.value.delete(id)
      needBatchSave = true
    }
  })

  // 保存清理后的状态
  if (needSave) {
    saveNotifiedTasks(notifiedTaskIds.value)
  }
  if (needBatchSave) {
    saveNotifiedBatchTasks(notifiedBatchTaskIds.value)
  }
})

// 切换面板显示
const togglePanel = () => {
  showPanel.value = !showPanel.value
}

// 清除错误任务
const clearErrorTasks = async () => {
  for (const task of errorTasks.value) {
    try {
      await deleteTask(task.id);
    } catch (error) {
      console.error(`删除错误任务失败: ${task.fileName}`, error);
      // 如果删除失败，至少从前端移除
      removeTask(task.id);
    }
  }
}

// 清除上传历史记录（异步）
const handleClearUploadHistory = async () => {
  try {
    await clearUploadHistory()
  } catch (error) {
    console.error('清除上传历史失败:', error)
  }
}

// 清除下载历史记录（异步）
const handleClearDownloadHistory = async () => {
  try {
    await clearDownloadHistory()
  } catch (error) {
    console.error('清除下载历史失败:', error)
  }
}

// 清除全部历史记录（异步）
const handleClearHistory = async () => {
  try {
    await clearHistory()
  } catch (error) {
    console.error('清除全部历史失败:', error)
  }
}

// 重试历史任务
const retryHistoryTask = async (historyId: string) => {
  try {
    // 从历史记录中找到任务信息
    const historyTask = allHistory.value.find(task => task.id === historyId)
    if (!historyTask) {
      toast.error('未找到历史任务')
      return
    }

    // 只允许重试失败或取消的任务
    if (historyTask.status === 'completed') {
      toast.warning('已完成的任务无需重试')
      return
    }

    console.log(`🔄 开始重试历史任务: ${historyTask.id}`)

    if ('batchName' in historyTask) {
      // 批量任务重试

      // 检查批量任务是否还在 TUS 系统中
      const batchTask = Array.from(tusUpload.batchTasks.value.values())
        .find(batch => batch.id === historyTask.id || batch.batchName === historyTask.batchName)

      if (batchTask) {
        // 如果批量任务还在，直接重试
        const success = await tusUpload.retryBatchUpload(batchTask.id)
        if (success) {
          // 从历史记录中移除，因为任务重新激活了
          removeHistoryItem(historyId)
        } else {
          toast.error(`批量任务 "${historyTask.batchName}" 重试失败`)
        }
      } else {
        // 批量任务不在 TUS 系统中，无法重试
        toast.warning(`批量任务 "${historyTask.batchName}" 已过期，无法重试。请重新上传文件夹。`)
      }
    } else {
      // 普通任务重试

      // 检查任务是否还在 TUS 系统中
      const tusTask = Array.from(tusUpload.tasks.value.values())
        .find(task => task.id === historyTask.id || task.fileName === historyTask.fileName)

      if (tusTask) {
        // 如果任务还在，直接重试
        const success = await tusUpload.retryUpload(tusTask.id)
        if (success) {
          // 从历史记录中移除，因为任务重新激活了
          removeHistoryItem(historyId)
        } else {
          toast.error(`文件 "${historyTask.fileName}" 重试失败`)
        }
      } else {
        // 任务不在 TUS 系统中，无法重试
        toast.warning(`文件 "${historyTask.fileName}" 已过期，无法重试。请重新上传文件。`)
      }
    }
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error)
    toast.error(`重试失败: ${errorMsg}`)
  }
}

// 暂停任务
const handlePauseTask = async (taskId: string) => {
  try {
    const success = await pauseTask(taskId)
    if (!success) {
      console.error('暂停任务失败')
    }
  } catch (error) {
    console.error('暂停任务出错:', error)
  }
}

// 恢复任务
const handleResumeTask = async (taskId: string) => {
  try {
    const success = await resumeTask(taskId)
    if (!success) {
      console.error('恢复任务失败')
    }
  } catch (error) {
    console.error('恢复任务出错:', error)
  }
}

// 取消任务 - 修改为异步
const handleCancelTask = async (taskId: string) => {
  try {
    const success = await cancelTask(taskId)
    if (!success) {
      console.error('取消任务失败')
    }
  } catch (error) {
    console.error('取消任务出错:', error)
  }
}
</script>