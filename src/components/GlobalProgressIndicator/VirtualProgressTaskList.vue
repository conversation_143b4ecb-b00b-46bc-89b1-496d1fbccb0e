<template>
  <div class="virtual-progress-task-list">
    <!-- 虚拟滚动容器 -->
    <div 
      ref="containerRef"
      class="virtual-scroll-container"
      :style="{ height: containerHeight + 'px' }"
      @scroll="handleScroll"
    >
      <!-- 占位空间 - 上方 -->
      <div :style="{ height: offsetY + 'px' }"></div>
      
      <!-- 可见任务列表 -->
      <div class="visible-tasks space-y-2">
        <template v-for="(item, index) in visibleItems" :key="item.id">
          <!-- 批次管理器 -->
          <BaseBatchTaskItem 
            v-if="item.type === 'batch-manager'"
            :batch-task="item"
            :sub-tasks="getBatchSubTasks(item.id)"
            :task-type="item.batchType || 'upload'"
            :loading="false"
            @cancel="handleBatchCancel"
            @retry="handleBatchRetry"
            @pause="handleBatchPause"
            @resume="handleBatchResume"
            @toggle-expanded="handleBatchToggleExpanded"
          />
          
          <!-- 批量任务 -->
          <BaseBatchTaskItem
            v-else-if="item.type === 'batch'"
            :batch-task="item"
            :sub-tasks="getBatchSubTasks(item.id)"
            :task-type="item.batchType || 'upload'"
            :loading="false"
            @cancel="handleBatchCancel"
            @retry="handleBatchRetry"
            @pause="handleBatchPause"
            @resume="handleBatchResume"
            @toggle-expanded="handleBatchToggleExpanded"
          />
          
          <!-- 批量历史任务 -->
          <BatchHistoryTaskItem
            v-else-if="'batchName' in item"
            :batch-task="item"
            @retry="$emit('retryHistoryTask', $event)"
            @remove="$emit('removeHistoryItem', $event)"
          />
          
          <!-- 普通历史任务 -->
          <HistoryTaskItem
            v-else-if="item.type === 'history'"
            :task="item"
            @retry="$emit('retryHistoryTask', $event)"
            @remove="$emit('removeHistoryItem', $event)"
          />
          
          <!-- 普通进度任务 -->
          <ProgressTaskItem
            v-else
            :task="item"
            @cancel="$emit('cancelTask', $event)"
            @remove="$emit('removeTask', $event)"
            @retry="$emit('retryTask', $event)"
            @pause="$emit('pauseTask', $event)"
            @resume="$emit('resumeTask', $event)"
          />
        </template>
      </div>
      
      <!-- 占位空间 - 下方 -->
      <div :style="{ height: (totalHeight - offsetY - visibleHeight) + 'px' }"></div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="totalItems === 0" class="empty-state py-8 text-center text-muted-foreground">
      <div class="text-sm">{{ emptyMessage }}</div>
      <div class="mt-1 text-xs">{{ emptySubMessage }}</div>
    </div>
    
    <!-- 性能统计（开发模式） -->
    <div v-if="showPerformanceStats" class="performance-stats">
      <div class="stat-item">
        <span>总数:</span>
        <span>{{ totalItems }}</span>
      </div>
      <div class="stat-item">
        <span>可见:</span>
        <span>{{ visibleItems.length }}</span>
      </div>
      <div class="stat-item">
        <span>渲染:</span>
        <span>{{ renderTime }}ms</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useVirtualScroll } from '@/components/Upload/composables/useVirtualScroll'
import { usePerformanceMonitor } from '@/components/Upload/composables/usePerformanceMonitor'
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import { useStreamDownloadManager } from '@/composables/useStreamDownloadManager'
import ProgressTaskItem from './ProgressTaskItem.vue'
import HistoryTaskItem from './HistoryTaskItem.vue'
import BaseBatchTaskItem from './BaseBatchTaskItem.vue'
import BatchHistoryTaskItem from './BatchHistoryTaskItem.vue'
import type { ProgressTask, HistoryTask, BatchHistoryTask } from '@/composables/useGlobalProgress'

interface Props {
  items: (ProgressTask | HistoryTask | BatchHistoryTask | any)[]
  containerHeight?: number
  itemHeight?: number
  bufferSize?: number
  showPerformanceStats?: boolean
  emptyMessage?: string
  emptySubMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  containerHeight: 264, // max-h-64 的像素值
  itemHeight: 60,
  bufferSize: 3,
  showPerformanceStats: false,
  emptyMessage: '暂无任务',
  emptySubMessage: '点击上传或下载按钮开始使用'
})

const emit = defineEmits<{
  cancelTask: [taskId: string]
  removeTask: [taskId: string]
  retryTask: [taskId: string]
  pauseTask: [taskId: string]
  resumeTask: [taskId: string]
  retryHistoryTask: [historyId: string]
  removeHistoryItem: [historyId: string]
}>()

// 组合式API
const tusUpload = useTusUpload()
const downloadManager = useStreamDownloadManager()
const containerRef = ref<HTMLElement>()

// 性能监控
const {
  renderTime,
  startMeasure,
  endMeasure
} = usePerformanceMonitor()

// 虚拟滚动
const {
  visibleItems,
  offsetY,
  totalHeight,
  visibleHeight,
  handleScroll,
  updateItemHeight
} = useVirtualScroll({
  items: computed(() => props.items),
  containerHeight: props.containerHeight,
  itemHeight: props.itemHeight,
  bufferSize: props.bufferSize
})

// 计算属性
const totalItems = computed(() => props.items.length)

// 方法
const getBatchSubTasks = (batchId: string) => {
  // 先尝试从上传批量任务获取
  const uploadSubTasks = tusUpload.getBatchSubTasks(batchId)
  if (uploadSubTasks.length > 0) {
    return uploadSubTasks
  }
  
  // 再尝试从下载批量任务获取
  const batchTask = downloadManager.batchTasksMap.value.get(batchId)
  if (batchTask) {
    return batchTask.subTasks.map(taskId => {
      return downloadManager.tasksMap.value.get(taskId)
    }).filter((task): task is NonNullable<typeof task> => Boolean(task))
  }
  
  return []
}

const handleBatchCancel = async (batchId: string) => {
  try {
    // 先尝试取消上传批量任务
    const uploadBatch = Array.from(tusUpload.batchTasks.value.values()).find(b => b.id === batchId)
    if (uploadBatch) {
      await tusUpload.deleteBatchTask(batchId)
      return
    }
    
    // 再尝试取消下载批量任务
    const downloadBatch = downloadManager.batchTasksMap.value.get(batchId)
    if (downloadBatch) {
      await downloadManager.cancelBatchDownload(batchId)
      downloadManager.removeBatchTask(batchId)
    }
  } catch (error) {
    console.error('取消批量任务失败:', error)
  }
}

const handleBatchRetry = async (batchId: string) => {
  try {
    // 先尝试重试上传批量任务
    const uploadBatch = Array.from(tusUpload.batchTasks.value.values()).find(b => b.id === batchId)
    if (uploadBatch) {
      await tusUpload.retryBatchUpload(batchId)
      return
    }
    
    // 下载批量任务重试逻辑可以在这里添加
    console.log('重试下载批量任务:', batchId)
  } catch (error) {
    console.error('重试批量任务失败:', error)
  }
}

const handleBatchPause = async (batchId: string) => {
  try {
    // 先尝试暂停上传批量任务
    const uploadBatch = Array.from(tusUpload.batchTasks.value.values()).find(b => b.id === batchId)
    if (uploadBatch) {
      await tusUpload.pauseBatchUpload(batchId)
      return
    }
    
    // 再尝试暂停下载批量任务
    const downloadBatch = downloadManager.batchTasksMap.value.get(batchId)
    if (downloadBatch) {
      await downloadManager.pauseBatchDownload(batchId)
    }
  } catch (error) {
    console.error('暂停批量任务失败:', error)
  }
}

const handleBatchResume = async (batchId: string) => {
  try {
    // 先尝试恢复上传批量任务
    const uploadBatch = Array.from(tusUpload.batchTasks.value.values()).find(b => b.id === batchId)
    if (uploadBatch) {
      await tusUpload.resumeBatchUpload(batchId)
      return
    }
    
    // 再尝试恢复下载批量任务
    const downloadBatch = downloadManager.batchTasksMap.value.get(batchId)
    if (downloadBatch) {
      await downloadManager.resumeBatchDownload(batchId)
    }
  } catch (error) {
    console.error('恢复批量任务失败:', error)
  }
}

const handleBatchToggleExpanded = (batchId: string) => {
  // 先尝试切换上传批量任务展开状态
  const uploadBatch = Array.from(tusUpload.batchTasks.value.values()).find(b => b.id === batchId)
  if (uploadBatch) {
    tusUpload.toggleBatchExpanded(batchId)
    // 更新项目高度
    const newHeight = uploadBatch.expanded ? props.itemHeight * 2 : props.itemHeight
    updateItemHeight(batchId, newHeight)
    return
  }
  
  // 再尝试切换下载批量任务展开状态
  const downloadBatch = downloadManager.batchTasksMap.value.get(batchId)
  if (downloadBatch) {
    const updatedBatch = { ...downloadBatch, expanded: !downloadBatch.expanded }
    downloadManager.batchTasksMap.value.set(batchId, updatedBatch)
    // 更新项目高度
    const newHeight = updatedBatch.expanded ? props.itemHeight * 2 : props.itemHeight
    updateItemHeight(batchId, newHeight)
  }
}

// 性能优化：监听数据变化
watch(() => props.items, () => {
  startMeasure('render')
  nextTick(() => {
    endMeasure('render')
  })
}, { deep: false })
</script>

<style scoped>
.virtual-progress-task-list {
  position: relative;
  width: 100%;
}

.virtual-scroll-container {
  overflow-y: auto;
  overflow-x: hidden;
}

.visible-tasks {
  position: relative;
}

.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.performance-stats {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  z-index: 1000;
  display: flex;
  gap: 8px;
}

.stat-item {
  display: flex;
  gap: 2px;
}
</style>
