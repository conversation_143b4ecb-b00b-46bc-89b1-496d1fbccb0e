<template>
  <div class="p-4 mb-2 w-96 max-w-sm rounded-lg border shadow-lg bg-background border-border">
    <!-- 头部信息 -->
    <div class="flex justify-between items-center mb-3">
      <div class="flex gap-2 items-center">
        <div class="relative">
          <component
            :is="hasActiveTasks || hasBatchTasks ? (uploadTasks.length > 0 || batchTasks.length > 0 ? UploadIcon : DownloadIcon) : AlertCircleIcon"
            :class="cn(
              'h-2 w-2',
              hasActiveTasks || hasBatchTasks ? 'text-primary' : 'text-red-500',
              (hasActiveTasks || hasBatchTasks) && 'animate-pulse'
            )" />
        </div>
        <span class="text-sm font-medium whitespace-nowrap">
          {{ hasActiveTasks || hasBatchTasks ? `${totalActiveCount} 个任务进行中` : errorTasks.length > 0 ?
            `${errorTasks.length} 个任务失败` :
            '任务管理器' }}
        </span>
      </div>
      <Button variant="ghost" size="sm" @click="$emit('toggleMinimized')">
        <component :is="minimized ? ChevronUpIcon : ChevronDownIcon" class="w-4 h-4" />
      </Button>
    </div>

    <!-- 标签页导航 -->
    <div v-if="!minimized" class="flex mb-3 border-b">
      <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key" :class="cn(
        'flex-1 py-2 px-3 text-sm font-medium border-b-2 transition-colors',
        activeTab === tab.key
          ? 'border-primary text-primary'
          : 'border-transparent text-muted-foreground hover:text-foreground'
      )">
        {{ tab.label }}
        <span v-if="tab.count > 0" class="ml-1 text-xs">({{ tab.count }})</span>
      </button>
    </div>

    <!-- 总体进度条 -->
    <div v-if="hasActiveTasks || hasBatchTasks" class="mb-3">
      <div class="flex justify-between items-center mb-1 text-xs text-muted-foreground">
        <span>总体进度</span>
        <span>{{ combinedProgress }}%</span>
      </div>
      <div class="w-full h-2 rounded-full bg-secondary">
        <div class="h-2 rounded-full transition-all duration-300 bg-primary"
          :style="{ width: `${combinedProgress}%` }" />
      </div>
    </div>

    <!-- 任务列表 - 使用虚拟滚动 -->
    <div v-if="!minimized">
      <VirtualProgressTaskList :items="currentDisplayItems" :container-height="264" :item-height="60" :buffer-size="3"
        :empty-message="currentEmptyMessage" :empty-sub-message="currentEmptySubMessage"
        @cancel-task="$emit('cancelTask', $event)" @remove-task="$emit('removeTask', $event)"
        @retry-task="$emit('retryTask', $event)" @pause-task="$emit('pauseTask', $event)"
        @resume-task="$emit('resumeTask', $event)" @retry-history-task="$emit('retryHistoryTask', $event)"
        @remove-history-item="$emit('removeHistoryItem', $event)" />
    </div>

    <!-- 操作按钮 -->
    <div v-if="!minimized" class="pt-3 mt-3 border-t">
      <!-- 当前任务操作 -->
      <div v-if="activeTab === 'current' && (totalActiveCount > 0 || errorTasks.length > 0)" class="flex gap-2">
        <Button v-if="errorTasks.length > 0" variant="outline" size="sm" @click="$emit('clearErrorTasks')"
          class="flex-1">
          清除错误
        </Button>
        <Button variant="outline" size="sm" @click="$emit('cleanupDuplicates')" class="flex-1">
          清理重复
        </Button>
        <Button variant="outline" size="sm" @click="handleClearAllTasks" class="flex-1">
          清除全部
        </Button>
      </div>

      <!-- 历史记录操作 -->
      <div v-else-if="activeTab !== 'current' && currentHistoryTasks.length > 0" class="flex gap-2">
        <Button v-if="activeTab === 'upload'" variant="outline" size="sm" @click="handleClearUploadHistory"
          :disabled="clearingHistory" class="flex-1">
          <Loader2Icon v-if="clearingHistory" class="mr-1 w-3 h-3 animate-spin" />
          清除上传历史
        </Button>
        <Button v-if="activeTab === 'download'" variant="outline" size="sm" @click="handleClearDownloadHistory"
          :disabled="clearingHistory" class="flex-1">
          <Loader2Icon v-if="clearingHistory" class="mr-1 w-3 h-3 animate-spin" />
          清除下载历史
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onUnmounted } from 'vue'
import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  AlertCircle as AlertCircleIcon,
  ChevronUp as ChevronUpIcon,
  ChevronDown as ChevronDownIcon,
  Loader2 as Loader2Icon
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import VirtualProgressTaskList from './VirtualProgressTaskList.vue'
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import { useStreamDownloadManager } from '@/composables/useStreamDownloadManager'
import type { ProgressTask, HistoryTask, BatchHistoryTask } from '@/composables/useGlobalProgress'

interface Props {
  activeTasks: ProgressTask[]
  uploadTasks: ProgressTask[]
  errorTasks: ProgressTask[]
  hasActiveTasks: boolean
  overallProgress: number
  minimized: boolean
  uploadHistory: (HistoryTask | BatchHistoryTask)[]
  downloadHistory: (HistoryTask | BatchHistoryTask)[]
  allHistory: (HistoryTask | BatchHistoryTask)[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  toggleMinimized: []
  cancelTask: [taskId: string]
  removeTask: [taskId: string]
  retryTask: [taskId: string]
  clearErrorTasks: []
  clearAllTasks: []
  cleanupDuplicates: []
  clearUploadHistory: []
  clearDownloadHistory: []
  clearHistory: []
  removeHistoryItem: [historyId: string]
  retryHistoryTask: [historyId: string]
  pauseTask: [taskId: string]
  resumeTask: [taskId: string]
}>()

// 集成 TUS 上传功能
const tusUpload = useTusUpload()
const downloadManager = useStreamDownloadManager()

// 当前活跃标签页
const activeTab = ref<'current' | 'upload' | 'download'>('current')

// Loading 状态
const clearingHistory = ref(false)

// 已处理的批量任务ID集合，避免重复清除
const processedBatchTasks = ref<Set<string>>(new Set())
const processedDownloadBatchTasks = ref<Set<string>>(new Set())

// 用于跟踪定时器，确保组件卸载时能够清理
const cleanupTimers = ref<Set<NodeJS.Timeout>>(new Set())

// 获取批量任务
const batchTasks = computed(() => tusUpload.batchTasks.value)
const activeBatchTasks = computed(() => Array.from(batchTasks.value.values()).filter(task =>
  ['pending', 'uploading'].includes(task.status)
))

// 获取下载批量任务
const downloadBatchTasks = computed(() => downloadManager.batchTasks.value)
const activeDownloadBatchTasks = computed(() => downloadBatchTasks.value.filter(task =>
  ['pending', 'downloading', 'paused'].includes(task.status)
))

const hasBatchTasks = computed(() => activeBatchTasks.value.length > 0 || activeDownloadBatchTasks.value.length > 0)

// 计算总活跃任务数（包括批量任务）
const totalActiveCount = computed(() => {
  return props.activeTasks.length + activeBatchTasks.value.length + activeDownloadBatchTasks.value.length
})

// 计算组合进度
const combinedProgress = computed(() => {
  const regularTasks = props.activeTasks
  const batchTasksActive = activeBatchTasks.value
  const downloadBatchTasksActive = activeDownloadBatchTasks.value

  const allActiveTasks = [...regularTasks, ...batchTasksActive, ...downloadBatchTasksActive]
  if (allActiveTasks.length === 0) return 0

  const totalProgress = allActiveTasks.reduce((sum, task) => sum + task.progress, 0)
  return Math.round(totalProgress / allActiveTasks.length)
})

// 标签页配置
const tabs = computed(() => [
  {
    key: 'current' as const,
    label: '当前任务',
    count: totalActiveCount.value + props.errorTasks.length
  },
  {
    key: 'upload' as const,
    label: '上传历史',
    count: props.uploadHistory.length
  },
  {
    key: 'download' as const,
    label: '下载历史',
    count: props.downloadHistory.length
  }
])

// 合并活跃任务和错误任务（批量任务已不在 tasks 中，无需过滤）
const allTasks = computed(() => {
  return [...props.activeTasks, ...props.errorTasks]
})

// 当前显示的历史任务
const currentHistoryTasks = computed(() => {
  if (activeTab.value === 'upload') {
    return props.uploadHistory.slice(0, 50) // 限制显示数量
  } else if (activeTab.value === 'download') {
    return props.downloadHistory.slice(0, 50) // 限制显示数量
  }
  return []
})

// 当前显示的项目（用于虚拟滚动）
const currentDisplayItems = computed(() => {
  if (activeTab.value === 'current') {
    // 当前任务：批量任务 + 普通任务
    const items: any[] = []

    // 添加上传批量任务
    items.push(...activeBatchTasks.value.map(task => ({
      ...task,
      type: 'batch',
      batchType: 'upload'
    })))

    // 添加下载批量任务
    items.push(...activeDownloadBatchTasks.value.map(task => ({
      ...task,
      type: 'batch',
      batchType: 'download'
    })))

    // 添加普通任务
    items.push(...allTasks.value)

    return items
  } else {
    // 历史记录任务
    return currentHistoryTasks.value.map(task => ({
      ...task,
      type: 'batchName' in task ? 'batch-history' : 'history'
    }))
  }
})

// 空状态消息
const currentEmptyMessage = computed(() => {
  if (activeTab.value === 'current') {
    return '暂无任务'
  } else {
    return `暂无${activeTab.value === 'upload' ? '上传' : '下载'}历史`
  }
})

const currentEmptySubMessage = computed(() => {
  if (activeTab.value === 'current') {
    return '点击上传或下载按钮开始使用'
  } else {
    return '完成任务后会在这里显示'
  }
})

// 监听批量任务状态变化，自动清除已完成的任务
watch(batchTasks, (newBatchTasks) => {
  const completedTasks = Array.from(newBatchTasks.values()).filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status) &&
    !processedBatchTasks.value.has(task.id) // 避免重复处理
  )

  // 延迟清除已完成的批量任务，给用户一些时间看到完成状态
  completedTasks.forEach(task => {
    // 标记为已处理
    processedBatchTasks.value.add(task.id)

    const timer = setTimeout(() => {
      console.log(`自动清除已完成的批量任务: ${task.batchName} (状态: ${task.status})`)
      tusUpload.deleteBatchTask(task.id)

      // 清除完成后从已处理集合中移除
      processedBatchTasks.value.delete(task.id)

      // 从定时器集合中移除
      cleanupTimers.value.delete(timer)
    }, 2000) // 2秒后自动清除

    // 将定时器添加到集合中以便清理
    cleanupTimers.value.add(timer)
  })
}, { deep: true })

// 监听下载批量任务状态变化，自动清除已完成的任务
watch(downloadBatchTasks, (newBatchTasks) => {
  const completedTasks = newBatchTasks.filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status) &&
    !processedDownloadBatchTasks.value.has(task.id) // 避免重复处理
  )

  // 延迟清除已完成的下载批量任务，给用户一些时间看到完成状态
  completedTasks.forEach(task => {
    // 标记为已处理
    processedDownloadBatchTasks.value.add(task.id)

    const timer = setTimeout(() => {
      console.log(`自动清除已完成的下载批量任务: ${task.batchName} (状态: ${task.status})`)
      downloadManager.removeBatchTask(task.id)

      // 清除完成后从已处理集合中移除
      processedDownloadBatchTasks.value.delete(task.id)

      // 从定时器集合中移除
      cleanupTimers.value.delete(timer)
    }, 2000) // 2秒后自动清除

    // 将定时器添加到集合中以便清理
    cleanupTimers.value.add(timer)
  })
}, { deep: true })

// 注意：批量任务操作现在由 VirtualProgressTaskList 组件内部处理

// 处理清除所有任务
const handleClearAllTasks = () => {
  // 清除普通任务
  emit('clearAllTasks')

  // 清除所有已完成的上传批量任务
  const completedBatchTasks = Array.from(batchTasks.value.values()).filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status)
  )

  completedBatchTasks.forEach(task => {
    tusUpload.deleteBatchTask(task.id)
  })

  // 清除所有已完成的下载批量任务
  const completedDownloadBatchTasks = downloadBatchTasks.value.filter(task =>
    ['completed', 'error', 'cancelled'].includes(task.status)
  )

  completedDownloadBatchTasks.forEach(task => {
    downloadManager.removeBatchTask(task.id)
  })
}

// 处理清除上传历史记录
const handleClearUploadHistory = async () => {
  if (clearingHistory.value) return

  clearingHistory.value = true
  try {
    await new Promise<void>((resolve) => {
      emit('clearUploadHistory')
      // 给一个短暂的延迟来显示loading状态
      setTimeout(resolve, 300)
    })
  } finally {
    clearingHistory.value = false
  }
}

// 处理清除下载历史记录
const handleClearDownloadHistory = async () => {
  if (clearingHistory.value) return

  clearingHistory.value = true
  try {
    await new Promise<void>((resolve) => {
      emit('clearDownloadHistory')
      // 给一个短暂的延迟来显示loading状态
      setTimeout(resolve, 300)
    })
  } finally {
    clearingHistory.value = false
  }
}

// 组件卸载时清理所有定时器
onUnmounted(() => {
  cleanupTimers.value.forEach(timer => {
    clearTimeout(timer)
  })
  cleanupTimers.value.clear()
})
</script>