/**
 * 任务数量显示修复验证脚本
 * 
 * 用于验证文件夹上传时任务数量显示是否正确
 */

import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import { useGlobalProgress } from '@/composables/useGlobalProgress'

// 创建模拟文件
function createMockFile(name: string, size: number = 1024): File {
  const content = new Array(size).fill('a').join('')
  const blob = new Blob([content], { type: 'text/plain' })
  return new File([blob], name, { type: 'text/plain' })
}

// 创建模拟文件夹结构
function createMockFolderFiles(folderName: string, fileCount: number): File[] {
  const files: File[] = []
  for (let i = 1; i <= fileCount; i++) {
    const fileName = `${folderName}/file-${i.toString().padStart(3, '0')}.txt`
    const fileSize = Math.floor(Math.random() * 2000) + 500 // 0.5KB - 2KB
    files.push(createMockFile(fileName, fileSize))
  }
  return files
}

// 测试配置
interface TaskCountTestConfig {
  name: string
  fileCount: number
  expectedTaskCount: number
  testType: 'folder' | 'batch' | 'individual'
}

const testConfigs: TaskCountTestConfig[] = [
  {
    name: '小文件夹上传测试',
    fileCount: 10,
    expectedTaskCount: 1, // 应该只显示1个批量任务
    testType: 'folder'
  },
  {
    name: '中等文件夹上传测试',
    fileCount: 50,
    expectedTaskCount: 1, // 应该只显示1个批量任务
    testType: 'folder'
  },
  {
    name: '大文件夹上传测试',
    fileCount: 100,
    expectedTaskCount: 1, // 应该只显示1个批量任务（可能分批）
    testType: 'folder'
  },
  {
    name: '分批上传测试',
    fileCount: 80,
    expectedTaskCount: 1, // 应该只显示1个批次管理器
    testType: 'batch'
  },
  {
    name: '独立文件上传测试',
    fileCount: 5,
    expectedTaskCount: 5, // 应该显示5个独立任务
    testType: 'individual'
  }
]

// 测试结果
interface TaskCountTestResult {
  configName: string
  fileCount: number
  expectedTaskCount: number
  actualTaskCount: number
  actualProgressTaskCount: number
  actualBatchTaskCount: number
  actualBatchManagerCount: number
  success: boolean
  error?: string
  details: {
    tusStandaloneTasks: number
    tusAllTasks: number
    tusBatchTasks: number
    tusBatchManagers: number
    progressTasks: number
    activeTasks: number
  }
}

/**
 * 运行任务数量测试
 */
async function runTaskCountTest(config: TaskCountTestConfig): Promise<TaskCountTestResult> {
  console.log(`\n🧪 开始测试: ${config.name}`)
  console.log(`文件数量: ${config.fileCount}, 预期任务数: ${config.expectedTaskCount}`)
  
  const tusUpload = useTusUpload()
  const globalProgress = useGlobalProgress()
  
  const result: TaskCountTestResult = {
    configName: config.name,
    fileCount: config.fileCount,
    expectedTaskCount: config.expectedTaskCount,
    actualTaskCount: 0,
    actualProgressTaskCount: 0,
    actualBatchTaskCount: 0,
    actualBatchManagerCount: 0,
    success: false,
    details: {
      tusStandaloneTasks: 0,
      tusAllTasks: 0,
      tusBatchTasks: 0,
      tusBatchManagers: 0,
      progressTasks: 0,
      activeTasks: 0
    }
  }

  try {
    // 创建测试文件
    const files = createMockFolderFiles(`test-${config.name.replace(/\s+/g, '-')}`, config.fileCount)
    console.log(`✅ 创建了 ${files.length} 个测试文件`)

    // 根据测试类型执行不同的上传方式
    if (config.testType === 'individual') {
      // 逐个上传文件
      for (const file of files) {
        await tusUpload.uploadFiles([file], {
          category: 'test',
          source: 'task-count-test-individual'
        })
      }
    } else {
      // 批量上传（文件夹或分批）
      await tusUpload.uploadFiles(files, {
        category: 'test',
        source: 'task-count-test-batch',
        testType: config.testType
      })
    }

    // 等待任务创建完成
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 收集统计信息
    result.details.tusStandaloneTasks = tusUpload.standaloneTasks.value.length
    result.details.tusAllTasks = tusUpload.tasks.value.length
    result.details.tusBatchTasks = tusUpload.batchTasks.value.length
    result.details.tusBatchManagers = tusUpload.batchManagers.value.length
    result.details.progressTasks = globalProgress.tasks.value.length
    result.details.activeTasks = globalProgress.activeTasks.value.length

    // 计算实际任务数量
    if (config.testType === 'individual') {
      // 独立上传：应该等于进度任务数量
      result.actualTaskCount = result.details.progressTasks
    } else {
      // 批量上传：应该等于批量任务数 + 批次管理器数
      result.actualTaskCount = result.details.tusBatchTasks + result.details.tusBatchManagers
    }

    result.actualProgressTaskCount = result.details.progressTasks
    result.actualBatchTaskCount = result.details.tusBatchTasks
    result.actualBatchManagerCount = result.details.tusBatchManagers

    // 判断测试是否成功
    result.success = result.actualTaskCount === config.expectedTaskCount

    console.log(`📊 测试结果:`)
    console.log(`   - 预期任务数: ${config.expectedTaskCount}`)
    console.log(`   - 实际任务数: ${result.actualTaskCount}`)
    console.log(`   - 进度任务数: ${result.actualProgressTaskCount}`)
    console.log(`   - 批量任务数: ${result.actualBatchTaskCount}`)
    console.log(`   - 批次管理器数: ${result.actualBatchManagerCount}`)
    console.log(`   - 测试状态: ${result.success ? '✅ 通过' : '❌ 失败'}`)

    if (!result.success) {
      console.log(`⚠️  任务数量不匹配！预期 ${config.expectedTaskCount}，实际 ${result.actualTaskCount}`)
    }

  } catch (error) {
    result.error = error instanceof Error ? error.message : String(error)
    console.error(`❌ 测试失败: ${config.name}`, error)
  }

  return result
}

/**
 * 运行所有任务数量测试
 */
export async function runAllTaskCountTests(): Promise<TaskCountTestResult[]> {
  console.log('🚀 开始任务数量显示修复验证测试...')
  
  const results: TaskCountTestResult[] = []
  
  for (const config of testConfigs) {
    const result = await runTaskCountTest(config)
    results.push(result)
    
    // 测试间隔，清理状态
    console.log('⏳ 清理测试状态...')
    const tusUpload = useTusUpload()
    tusUpload.cleanupMemory()
    await new Promise(resolve => setTimeout(resolve, 2000))
  }
  
  return results
}

/**
 * 打印测试结果汇总
 */
export function printTaskCountTestResults(results: TaskCountTestResult[]): void {
  console.log('\n📊 任务数量测试结果汇总:')
  console.log('=' .repeat(100))
  
  // 表头
  console.log(
    '测试名称'.padEnd(20) +
    '文件数'.padEnd(8) +
    '预期'.padEnd(6) +
    '实际'.padEnd(6) +
    '进度'.padEnd(6) +
    '批量'.padEnd(6) +
    '批次'.padEnd(6) +
    '状态'.padEnd(8)
  )
  console.log('-' .repeat(100))
  
  // 数据行
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败'
    console.log(
      result.configName.substring(0, 18).padEnd(20) +
      result.fileCount.toString().padEnd(8) +
      result.expectedTaskCount.toString().padEnd(6) +
      result.actualTaskCount.toString().padEnd(6) +
      result.actualProgressTaskCount.toString().padEnd(6) +
      result.actualBatchTaskCount.toString().padEnd(6) +
      result.actualBatchManagerCount.toString().padEnd(6) +
      status
    )
  })
  
  // 统计信息
  const passedTests = results.filter(r => r.success).length
  const totalTests = results.length
  const passRate = (passedTests / totalTests * 100).toFixed(1)
  
  console.log('\n📈 测试统计:')
  console.log(`   - 总测试数: ${totalTests}`)
  console.log(`   - 通过测试: ${passedTests}`)
  console.log(`   - 失败测试: ${totalTests - passedTests}`)
  console.log(`   - 通过率: ${passRate}%`)
  
  // 失败测试详情
  const failedTests = results.filter(r => !r.success)
  if (failedTests.length > 0) {
    console.log('\n❌ 失败测试详情:')
    failedTests.forEach(result => {
      console.log(`\n${result.configName}:`)
      console.log(`   - 预期任务数: ${result.expectedTaskCount}`)
      console.log(`   - 实际任务数: ${result.actualTaskCount}`)
      console.log(`   - TUS独立任务: ${result.details.tusStandaloneTasks}`)
      console.log(`   - TUS所有任务: ${result.details.tusAllTasks}`)
      console.log(`   - TUS批量任务: ${result.details.tusBatchTasks}`)
      console.log(`   - TUS批次管理器: ${result.details.tusBatchManagers}`)
      console.log(`   - 进度任务: ${result.details.progressTasks}`)
      console.log(`   - 活跃任务: ${result.details.activeTasks}`)
      if (result.error) {
        console.log(`   - 错误信息: ${result.error}`)
      }
    })
  }
}

/**
 * 生成修复验证报告
 */
export function generateTaskCountFixReport(results: TaskCountTestResult[]): string {
  const timestamp = new Date().toISOString()
  
  let report = `# 任务数量显示修复验证报告\n\n`
  report += `生成时间: ${timestamp}\n\n`
  
  const passedTests = results.filter(r => r.success).length
  const totalTests = results.length
  
  report += `## 测试概览\n\n`
  report += `- 总测试数: ${totalTests}\n`
  report += `- 通过测试: ${passedTests}\n`
  report += `- 失败测试: ${totalTests - passedTests}\n`
  report += `- 通过率: ${(passedTests / totalTests * 100).toFixed(1)}%\n\n`
  
  report += `## 修复效果\n\n`
  if (passedTests === totalTests) {
    report += `✅ **修复成功**: 所有测试通过，任务数量显示问题已完全解决。\n\n`
  } else {
    report += `⚠️ **部分修复**: ${passedTests}/${totalTests} 个测试通过，仍有部分问题需要解决。\n\n`
  }
  
  report += `## 详细结果\n\n`
  results.forEach(result => {
    report += `### ${result.configName}\n\n`
    report += `- 文件数量: ${result.fileCount}\n`
    report += `- 预期任务数: ${result.expectedTaskCount}\n`
    report += `- 实际任务数: ${result.actualTaskCount}\n`
    report += `- 测试状态: ${result.success ? '✅ 通过' : '❌ 失败'}\n`
    
    if (!result.success) {
      report += `- 问题分析:\n`
      report += `  - TUS独立任务: ${result.details.tusStandaloneTasks}\n`
      report += `  - TUS批量任务: ${result.details.tusBatchTasks}\n`
      report += `  - TUS批次管理器: ${result.details.tusBatchManagers}\n`
      report += `  - 进度任务: ${result.details.progressTasks}\n`
    }
    
    report += `\n`
  })
  
  return report
}

// 导出便捷的测试运行函数
export async function runTaskCountFixVerification(): Promise<void> {
  try {
    console.log('🎯 开始任务数量显示修复验证...')
    
    const results = await runAllTaskCountTests()
    printTaskCountTestResults(results)
    
    const report = generateTaskCountFixReport(results)
    console.log('\n📄 验证报告已生成')
    
    // 在浏览器环境中，可以下载报告
    if (typeof window !== 'undefined') {
      const blob = new Blob([report], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `task-count-fix-report-${Date.now()}.md`
      a.click()
      URL.revokeObjectURL(url)
    }
    
    console.log('\n🎉 任务数量显示修复验证完成!')
    
  } catch (error) {
    console.error('❌ 验证测试运行失败:', error)
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && (window as any).__runTaskCountFixVerification) {
  runTaskCountFixVerification()
}
