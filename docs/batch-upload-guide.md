# 分批上传功能使用指南

## 概述

分批上传功能是为了解决大量小文件上传时可能导致的服务器过载问题而设计的。当文件数量超过指定阈值时，系统会自动将文件分成多个批次，串行处理每个批次，确保服务器稳定性和上传成功率。

## 核心特性

### 1. 自动分批
- 当文件数量超过配置的阈值（默认50个）时，自动启用分批上传模式
- 每批处理的文件数量可配置（默认15个）
- 支持动态调整批次大小，根据文件大小优化性能

### 2. 串行批次执行
- 等待当前批次的所有文件完成完整的上传+同步流程后，才开始下一批次
- 批次间可配置延迟时间（默认2秒），避免对服务器造成突发压力
- 保持现有的并发控制设置（maxConcurrent: 10）在单批次内有效

### 3. 进度跟踪
- 提供整体进度和当前批次进度的双重反馈
- 在UI中显示当前批次进度（如"第2批/共5批"）
- 支持实时进度更新和状态监控

### 4. 任务管理
- 支持暂停/恢复/取消整个分批上传任务
- 每个批次独立的错误处理和重试机制
- 保持现有接口的向后兼容性

## 配置选项

```typescript
interface BatchUploadConfig {
  enableBatching: boolean;        // 是否启用分批上传
  batchThreshold: number;         // 启用分批的文件数量阈值
  batchSize: number;              // 每批处理的文件数量
  batchDelay: number;             // 批次间延迟（毫秒）
  dynamicBatchSize: boolean;      // 是否根据文件大小动态调整
  maxBatchSize: number;           // 最大批次大小
  minBatchSize: number;           // 最小批次大小
}
```

### 默认配置
```typescript
{
  enableBatching: true,
  batchThreshold: 50,     // 50个文件以上启用分批
  batchSize: 15,          // 每批15个文件
  batchDelay: 2000,       // 批次间延迟2秒
  dynamicBatchSize: true, // 启用动态批次大小
  maxBatchSize: 25,       // 最大批次25个文件
  minBatchSize: 5,        // 最小批次5个文件
}
```

## 使用方法

### 1. 基本使用
分批上传功能已集成到现有的 `uploadFiles` 方法中，无需修改现有代码：

```typescript
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'

const tusUpload = useTusUpload()

// 正常调用，系统会自动判断是否需要分批上传
await tusUpload.uploadFiles(files, metadata, callbacks)
```

### 2. 配置管理
```typescript
// 获取当前配置
const config = tusUpload.getBatchConfig()

// 更新配置
tusUpload.updateBatchConfig({
  batchThreshold: 100,  // 调整阈值为100个文件
  batchSize: 20,        // 调整批次大小为20个文件
  batchDelay: 3000      // 调整延迟为3秒
})
```

### 3. 批次管理器操作
```typescript
// 暂停分批上传
await tusUpload.pauseBatchManager(batchManagerId)

// 恢复分批上传
await tusUpload.resumeBatchManager(batchManagerId)

// 取消分批上传
await tusUpload.cancelBatchManager(batchManagerId)
```

### 4. 状态监控
```typescript
// 获取所有批次管理器
const batchManagers = tusUpload.batchManagers.value

// 监控批次状态
batchManagers.forEach(manager => {
  console.log(`批次 ${manager.id}:`, {
    status: manager.status,
    progress: manager.progress,
    currentBatch: manager.currentBatch,
    totalBatches: manager.totalBatches
  })
})
```

## 动态批次大小算法

系统会根据文件大小自动调整批次大小：

- **小文件（< 1MB）**：增加批次大小（1.5倍）
- **大文件（> 10MB）**：减少批次大小（0.7倍）
- **普通文件**：使用默认批次大小

最终批次大小会限制在 `minBatchSize` 和 `maxBatchSize` 之间。

## 性能优化建议

### 1. 根据服务器性能调整配置
- 服务器性能较好：可以增加 `batchSize` 和减少 `batchDelay`
- 服务器性能一般：保持默认配置或适当减少 `batchSize`
- 网络不稳定：增加 `batchDelay` 以减少网络压力

### 2. 文件类型优化
- 大量小文件：启用 `dynamicBatchSize`，系统会自动增加批次大小
- 混合文件大小：保持默认配置，让系统自动调整
- 大文件为主：可以禁用分批上传或设置较高的 `batchThreshold`

### 3. 监控和调试
```typescript
// 启用详细日志
tusUpload.enableAllLogs()

// 只显示批次相关日志
tusUpload.updateLogConfig({
  showBatch: true,
  showAnalysis: true,
  showTasks: false,
  showEvents: false
})
```

## 错误处理

### 1. 批次级错误处理
- 如果某批次中有文件失败，系统会记录错误但继续处理下一批次
- 可以通过批次管理器的状态监控错误情况
- 支持重试失败的批次

### 2. 整体错误处理
- 如果批次处理过程中发生严重错误，整个分批上传会停止
- 错误信息会通过 toast 通知用户
- 可以通过日志系统查看详细错误信息

## 兼容性说明

- 分批上传功能完全向后兼容，现有代码无需修改
- 所有现有的回调函数和事件监听器继续正常工作
- 现有的任务管理功能（暂停/恢复/取消）继续支持
- UI组件会自动显示分批上传的进度和状态

## 示例组件

项目中包含了一个完整的示例组件 `BatchUploadExample.vue`，展示了：
- 配置管理界面
- 实时状态监控
- 批次管理器操作
- 测试分批上传功能

可以参考这个组件来了解如何在实际项目中使用分批上传功能。
