import { contextBridge, ipc<PERSON>enderer } from "electron";
import { createSimplifiedTusPreloadApi } from "./tus/index";
import { createSimplifiedDownloadPreloadApi } from "./stream-downloader/index";

// 从 TUS 模块导出类型，保持向后兼容性
export type { UploadTask, TusUploadConfig } from "./tus/index";

// 从下载模块导出类型
export type { DownloadTask, StreamDownloadConfig } from "./stream-downloader/index";

// 创建简化的 TUS API（整合了 API 调用和事件监听器）
const tusApi = createSimplifiedTusPreloadApi();

// 创建简化的下载 API（整合了 API 调用和事件监听器）
const downloadApi = createSimplifiedDownloadPreloadApi();

// 渲染进程的自定义 API
const api = {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke("get-app-version"),
  getPlatform: () => ipcRenderer.invoke("get-platform"),

  // 对话框 API
  showOpenDialog: (options: Electron.OpenDialogOptions) => ipcRenderer.invoke("show-open-dialog", options),
  showSaveDialog: (options: Electron.SaveDialogOptions) => ipcRenderer.invoke("show-save-dialog", options),
  showMessageBox: (options: Electron.MessageBoxOptions) => ipcRenderer.invoke("show-message-box", options),

  // 文件系统 API
  getFileInfo: (filePath: string) => ipcRenderer.invoke("get-file-info", filePath),

  // TUS 上传 API（整合了 API 调用和事件监听器）
  tus: tusApi,

  // StreamSaver 下载 API（整合了 API 调用和事件监听器）
  download: downloadApi,

  // 主进程消息监听器
  onMainProcessMessage: (callback: (data: string) => void) => {
    ipcRenderer.on("main-process-message", (_event, data) => callback(data));
  },

  // 认证相关 API
  onAuthToken: (callback: (token: string) => void) => {
    ipcRenderer.on("auth-token", (_event, token) => callback(token));
  },

  clearAuthState: () => {
    ipcRenderer.send("clear-auth-state");
  },
};

// 使用 `contextBridge` API 向渲染进程暴露 Electron API
// 仅在启用上下文隔离时有效，否则直接添加到 DOM 全局对象
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld("electronAPI", api);
  } catch (error) {
    console.error(error);
  }
} else {
  // @ts-ignore (在 dts 文件中定义)
  window.electronAPI = api;
}
