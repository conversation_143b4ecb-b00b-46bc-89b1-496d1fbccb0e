import { EventEmitter } from "events";
import * as fs from "fs/promises";
import * as path from "path";
import type { UploadTask } from "./types";

// 内存管理配置
export interface MemoryConfig {
  maxMemoryUsage: number; // 最大内存使用量 (MB)
  maxConcurrentFiles: number; // 最大并发文件数
  filePoolSize: number; // 文件池大小
  cleanupInterval: number; // 清理间隔 (ms)
  enableStreaming: boolean; // 启用流式读取
  chunkSize: number; // 流式读取块大小
}

// 文件池项目
interface FilePoolItem {
  filePath: string;
  buffer?: Buffer;
  stream?: NodeJS.ReadableStream;
  lastAccessed: Date;
  size: number;
  refCount: number;
}

// 内存统计
export interface MemoryStats {
  totalMemoryUsage: number; // 总内存使用量 (MB)
  filePoolUsage: number; // 文件池内存使用量 (MB)
  activeFiles: number; // 活跃文件数
  poolSize: number; // 池大小
  hitRate: number; // 命中率
}

/**
 * 内存管理器
 * 负责管理文件读取、内存池和垃圾回收
 */
export class MemoryManager extends EventEmitter {
  private config: MemoryConfig;
  private filePool: Map<string, FilePoolItem> = new Map();
  private accessStats: Map<string, number> = new Map();
  private totalAccess: number = 0;
  private totalHits: number = 0;
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<MemoryConfig> = {}) {
    super();
    
    this.config = {
      maxMemoryUsage: 500, // 500MB
      maxConcurrentFiles: 50,
      filePoolSize: 20,
      cleanupInterval: 30000, // 30秒
      enableStreaming: true,
      chunkSize: 64 * 1024, // 64KB
      ...config
    };

    this.startCleanupTimer();
  }

  /**
   * 获取文件内容（优先从池中获取）
   */
  async getFileContent(filePath: string): Promise<Buffer | NodeJS.ReadableStream> {
    this.totalAccess++;
    
    // 检查文件池
    const poolItem = this.filePool.get(filePath);
    if (poolItem) {
      poolItem.lastAccessed = new Date();
      poolItem.refCount++;
      this.totalHits++;
      
      if (poolItem.buffer) {
        return poolItem.buffer;
      } else if (poolItem.stream) {
        return poolItem.stream;
      }
    }

    // 文件不在池中，需要读取
    return this.loadFile(filePath);
  }

  /**
   * 加载文件到内存池
   */
  private async loadFile(filePath: string): Promise<Buffer | NodeJS.ReadableStream> {
    try {
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;

      // 检查内存使用情况
      if (this.getCurrentMemoryUsage() + (fileSize / 1024 / 1024) > this.config.maxMemoryUsage) {
        // 内存不足，清理旧文件
        await this.cleanupOldFiles();
      }

      // 根据文件大小决定使用缓冲还是流
      if (this.config.enableStreaming && fileSize > this.config.chunkSize * 10) {
        return this.createFileStream(filePath, fileSize);
      } else {
        return this.loadFileToBuffer(filePath, fileSize);
      }
    } catch (error) {
      console.error(`加载文件失败: ${filePath}`, error);
      throw error;
    }
  }

  /**
   * 创建文件流
   */
  private async createFileStream(filePath: string, fileSize: number): Promise<NodeJS.ReadableStream> {
    const stream = (await import('fs')).createReadStream(filePath, {
      highWaterMark: this.config.chunkSize
    });

    // 添加到文件池
    const poolItem: FilePoolItem = {
      filePath,
      stream,
      lastAccessed: new Date(),
      size: fileSize,
      refCount: 1
    };

    this.addToPool(filePath, poolItem);
    return stream;
  }

  /**
   * 加载文件到缓冲区
   */
  private async loadFileToBuffer(filePath: string, fileSize: number): Promise<Buffer> {
    const buffer = await fs.readFile(filePath);

    // 添加到文件池
    const poolItem: FilePoolItem = {
      filePath,
      buffer,
      lastAccessed: new Date(),
      size: fileSize,
      refCount: 1
    };

    this.addToPool(filePath, poolItem);
    return buffer;
  }

  /**
   * 添加到文件池
   */
  private addToPool(filePath: string, poolItem: FilePoolItem) {
    // 检查池大小限制
    if (this.filePool.size >= this.config.filePoolSize) {
      this.evictLeastRecentlyUsed();
    }

    this.filePool.set(filePath, poolItem);
    this.accessStats.set(filePath, (this.accessStats.get(filePath) || 0) + 1);
    
    this.emit('file-loaded', filePath, poolItem.size);
  }

  /**
   * 释放文件引用
   */
  releaseFile(filePath: string) {
    const poolItem = this.filePool.get(filePath);
    if (poolItem) {
      poolItem.refCount = Math.max(0, poolItem.refCount - 1);
      
      // 如果没有引用且不是热点文件，可以考虑移除
      if (poolItem.refCount === 0) {
        const accessCount = this.accessStats.get(filePath) || 0;
        if (accessCount < 3) { // 访问次数少的文件
          this.removeFromPool(filePath);
        }
      }
    }
  }

  /**
   * 从池中移除文件
   */
  private removeFromPool(filePath: string) {
    const poolItem = this.filePool.get(filePath);
    if (poolItem) {
      this.filePool.delete(filePath);
      this.emit('file-evicted', filePath, poolItem.size);
    }
  }

  /**
   * 清理最近最少使用的文件
   */
  private evictLeastRecentlyUsed() {
    let oldestFile: string | null = null;
    let oldestTime = Date.now();

    for (const [filePath, poolItem] of this.filePool.entries()) {
      if (poolItem.refCount === 0 && poolItem.lastAccessed.getTime() < oldestTime) {
        oldestTime = poolItem.lastAccessed.getTime();
        oldestFile = filePath;
      }
    }

    if (oldestFile) {
      this.removeFromPool(oldestFile);
    }
  }

  /**
   * 清理旧文件
   */
  private async cleanupOldFiles() {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5分钟

    const filesToRemove: string[] = [];
    
    for (const [filePath, poolItem] of this.filePool.entries()) {
      if (poolItem.refCount === 0 && now - poolItem.lastAccessed.getTime() > maxAge) {
        filesToRemove.push(filePath);
      }
    }

    filesToRemove.forEach(filePath => {
      this.removeFromPool(filePath);
    });

    // 强制垃圾回收
    if (global.gc) {
      global.gc();
    }

    this.emit('cleanup-completed', filesToRemove.length);
  }

  /**
   * 获取当前内存使用情况
   */
  private getCurrentMemoryUsage(): number {
    let totalSize = 0;
    for (const poolItem of this.filePool.values()) {
      totalSize += poolItem.size;
    }
    return totalSize / 1024 / 1024; // 转换为MB
  }

  /**
   * 获取内存统计信息
   */
  getMemoryStats(): MemoryStats {
    const hitRate = this.totalAccess > 0 ? (this.totalHits / this.totalAccess) * 100 : 0;
    
    return {
      totalMemoryUsage: this.getCurrentMemoryUsage(),
      filePoolUsage: this.getCurrentMemoryUsage(),
      activeFiles: Array.from(this.filePool.values()).filter(item => item.refCount > 0).length,
      poolSize: this.filePool.size,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MemoryConfig>) {
    this.config = { ...this.config, ...newConfig };
    
    // 重启清理定时器
    this.stopCleanupTimer();
    this.startCleanupTimer();
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer() {
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldFiles();
    }, this.config.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    this.stopCleanupTimer();
    this.filePool.clear();
    this.accessStats.clear();
    this.totalAccess = 0;
    this.totalHits = 0;
  }
}

// 创建全局内存管理器实例
export const globalMemoryManager = new MemoryManager();
