import { EventEmitter } from "events";
import type { UploadTask } from "./types";

// IPC优化配置
export interface IpcOptimizerConfig {
  batchSize: number; // 批量发送大小
  throttleMs: number; // 节流时间
  maxQueueSize: number; // 最大队列大小
  enableCompression: boolean; // 启用压缩
  priorityLevels: number; // 优先级级别数
}

// 消息类型
export type MessageType = 'progress' | 'status' | 'error' | 'completion' | 'batch-update';

// IPC消息
export interface IpcMessage {
  type: MessageType;
  taskId: string;
  data: any;
  timestamp: number;
  priority: number;
}

// 批量消息
export interface BatchMessage {
  type: 'batch';
  messages: IpcMessage[];
  timestamp: number;
}

/**
 * IPC通信优化器
 * 负责批量发送、节流、优先级管理和压缩
 */
export class IpcOptimizer extends EventEmitter {
  private config: IpcOptimizerConfig;
  private messageQueue: Map<number, IpcMessage[]> = new Map(); // 按优先级分组的消息队列
  private throttleTimers: Map<MessageType, NodeJS.Timeout> = new Map();
  private lastSentTime: Map<string, number> = new Map(); // 每个任务的最后发送时间
  private sendCallback: (message: IpcMessage | BatchMessage) => void;

  constructor(
    sendCallback: (message: IpcMessage | BatchMessage) => void,
    config: Partial<IpcOptimizerConfig> = {}
  ) {
    super();
    
    this.sendCallback = sendCallback;
    this.config = {
      batchSize: 10,
      throttleMs: 100,
      maxQueueSize: 1000,
      enableCompression: true,
      priorityLevels: 3,
      ...config
    };

    // 初始化优先级队列
    for (let i = 0; i < this.config.priorityLevels; i++) {
      this.messageQueue.set(i, []);
    }

    // 启动定期发送
    this.startPeriodicSend();
  }

  /**
   * 发送消息（经过优化处理）
   */
  sendMessage(type: MessageType, taskId: string, data: any, priority: number = 1) {
    const message: IpcMessage = {
      type,
      taskId,
      data,
      timestamp: Date.now(),
      priority: Math.max(0, Math.min(priority, this.config.priorityLevels - 1))
    };

    // 检查是否需要节流
    if (this.shouldThrottle(message)) {
      this.addToQueue(message);
    } else {
      this.sendImmediately(message);
    }
  }

  /**
   * 批量发送进度更新
   */
  sendProgressBatch(updates: Array<{ taskId: string; progress: number; bytesUploaded: number; bytesTotal: number }>) {
    const messages: IpcMessage[] = updates.map(update => ({
      type: 'progress' as MessageType,
      taskId: update.taskId,
      data: update,
      timestamp: Date.now(),
      priority: 1
    }));

    this.sendBatchMessage(messages);
  }

  /**
   * 发送任务状态批量更新
   */
  sendStatusBatch(updates: Array<{ taskId: string; status: string; error?: string }>) {
    const messages: IpcMessage[] = updates.map(update => ({
      type: 'status' as MessageType,
      taskId: update.taskId,
      data: update,
      timestamp: Date.now(),
      priority: 0 // 状态更新优先级最高
    }));

    this.sendBatchMessage(messages);
  }

  /**
   * 检查是否需要节流
   */
  private shouldThrottle(message: IpcMessage): boolean {
    const lastSent = this.lastSentTime.get(`${message.taskId}-${message.type}`);
    const now = Date.now();
    
    if (!lastSent) {
      return false;
    }

    // 高优先级消息不节流
    if (message.priority === 0) {
      return false;
    }

    // 错误和完成消息不节流
    if (message.type === 'error' || message.type === 'completion') {
      return false;
    }

    return now - lastSent < this.config.throttleMs;
  }

  /**
   * 添加到队列
   */
  private addToQueue(message: IpcMessage) {
    const queue = this.messageQueue.get(message.priority);
    if (!queue) return;

    // 检查队列大小限制
    if (queue.length >= this.config.maxQueueSize / this.config.priorityLevels) {
      // 移除最旧的消息
      queue.shift();
      this.emit('queue-overflow', message.priority);
    }

    // 对于进度消息，如果已有同一任务的消息，替换它
    if (message.type === 'progress') {
      const existingIndex = queue.findIndex(m => m.taskId === message.taskId && m.type === 'progress');
      if (existingIndex >= 0) {
        queue[existingIndex] = message;
        return;
      }
    }

    queue.push(message);
  }

  /**
   * 立即发送消息
   */
  private sendImmediately(message: IpcMessage) {
    this.updateLastSentTime(message);
    this.sendCallback(message);
    this.emit('message-sent', message);
  }

  /**
   * 发送批量消息
   */
  private sendBatchMessage(messages: IpcMessage[]) {
    if (messages.length === 0) return;

    const batchMessage: BatchMessage = {
      type: 'batch',
      messages,
      timestamp: Date.now()
    };

    // 更新最后发送时间
    messages.forEach(message => {
      this.updateLastSentTime(message);
    });

    this.sendCallback(batchMessage);
    this.emit('batch-sent', messages.length);
  }

  /**
   * 更新最后发送时间
   */
  private updateLastSentTime(message: IpcMessage) {
    this.lastSentTime.set(`${message.taskId}-${message.type}`, message.timestamp);
  }

  /**
   * 定期发送队列中的消息
   */
  private startPeriodicSend() {
    setInterval(() => {
      this.flushQueues();
    }, this.config.throttleMs);
  }

  /**
   * 刷新所有队列
   */
  private flushQueues() {
    // 按优先级从高到低处理
    for (let priority = 0; priority < this.config.priorityLevels; priority++) {
      const queue = this.messageQueue.get(priority);
      if (!queue || queue.length === 0) continue;

      // 取出一批消息
      const batch = queue.splice(0, this.config.batchSize);
      
      if (batch.length === 1) {
        // 单个消息直接发送
        this.sendImmediately(batch[0]);
      } else if (batch.length > 1) {
        // 多个消息批量发送
        this.sendBatchMessage(batch);
      }
    }
  }

  /**
   * 强制刷新所有队列
   */
  forceFlush() {
    for (let priority = 0; priority < this.config.priorityLevels; priority++) {
      const queue = this.messageQueue.get(priority);
      if (!queue || queue.length === 0) continue;

      // 发送所有消息
      const allMessages = queue.splice(0);
      if (allMessages.length > 0) {
        this.sendBatchMessage(allMessages);
      }
    }
  }

  /**
   * 获取队列统计信息
   */
  getQueueStats() {
    const stats: Record<number, number> = {};
    let totalMessages = 0;

    for (let priority = 0; priority < this.config.priorityLevels; priority++) {
      const queue = this.messageQueue.get(priority);
      const count = queue ? queue.length : 0;
      stats[priority] = count;
      totalMessages += count;
    }

    return {
      byPriority: stats,
      total: totalMessages,
      maxCapacity: this.config.maxQueueSize
    };
  }

  /**
   * 清理特定任务的消息
   */
  clearTaskMessages(taskId: string) {
    for (const queue of this.messageQueue.values()) {
      for (let i = queue.length - 1; i >= 0; i--) {
        if (queue[i].taskId === taskId) {
          queue.splice(i, 1);
        }
      }
    }

    // 清理最后发送时间记录
    for (const key of this.lastSentTime.keys()) {
      if (key.startsWith(taskId + '-')) {
        this.lastSentTime.delete(key);
      }
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<IpcOptimizerConfig>) {
    this.config = { ...this.config, ...newConfig };
    
    // 如果优先级级别数改变，重新初始化队列
    if (newConfig.priorityLevels) {
      this.messageQueue.clear();
      for (let i = 0; i < this.config.priorityLevels; i++) {
        this.messageQueue.set(i, []);
      }
    }
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    // 强制发送所有待发送的消息
    this.forceFlush();
    
    // 清理定时器
    for (const timer of this.throttleTimers.values()) {
      clearTimeout(timer);
    }
    this.throttleTimers.clear();
    
    // 清理队列
    this.messageQueue.clear();
    this.lastSentTime.clear();
  }
}

// 消息压缩工具
export class MessageCompressor {
  /**
   * 压缩消息数据
   */
  static compress(data: any): string {
    try {
      const jsonString = JSON.stringify(data);
      // 这里可以使用更高效的压缩算法，如 zlib
      return Buffer.from(jsonString).toString('base64');
    } catch (error) {
      console.error('消息压缩失败:', error);
      return JSON.stringify(data);
    }
  }

  /**
   * 解压消息数据
   */
  static decompress(compressedData: string): any {
    try {
      const jsonString = Buffer.from(compressedData, 'base64').toString();
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('消息解压失败:', error);
      return compressedData;
    }
  }
}
