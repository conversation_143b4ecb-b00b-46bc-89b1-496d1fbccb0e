import { EventEmitter } from "events";
import type { ProgressData, AggregatedProgress, QueueEvents } from "./types";

/**
 * 进度聚合器
 * 聚合多个任务的进度更新，实现节流机制以减少UI更新频率
 */
export class ProgressAggregator extends EventEmitter {
  private progressBuffer: Map<string, ProgressData> = new Map();
  private updateInterval: number;
  private timer: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;

  constructor(updateInterval: number = 500) {
    super();
    this.updateInterval = updateInterval;
  }

  /**
   * 更新任务进度
   */
  updateProgress(taskId: string, progress: number, bytesUploaded: number, bytesTotal: number, uploadSpeed: number = 0, remainingTime: number = 0): void {
    const progressData: ProgressData = {
      taskId,
      progress,
      bytesUploaded,
      bytesTotal,
      uploadSpeed,
      remainingTime,
      timestamp: Date.now(),
    };

    this.progressBuffer.set(taskId, progressData);

    // 如果聚合器没有运行，启动它
    if (!this.isRunning) {
      this.startAggregation();
    }
  }

  /**
   * 移除任务的进度数据
   */
  removeProgress(taskId: string): void {
    this.progressBuffer.delete(taskId);
  }

  /**
   * 启动进度聚合
   */
  startAggregation(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.scheduleNextFlush();
  }

  /**
   * 停止进度聚合
   */
  stopAggregation(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.isRunning = false;

    // 发送最后一次更新
    if (this.progressBuffer.size > 0) {
      this.flushUpdates();
    }
  }

  /**
   * 立即刷新所有待处理的进度更新
   */
  flushNow(): void {
    if (this.progressBuffer.size > 0) {
      this.flushUpdates();
    }
  }

  /**
   * 获取当前缓冲的进度数据数量
   */
  getBufferSize(): number {
    return this.progressBuffer.size;
  }

  /**
   * 设置更新间隔
   */
  setUpdateInterval(interval: number): void {
    this.updateInterval = interval;
    
    // 如果正在运行，重新调度
    if (this.isRunning) {
      this.stopAggregation();
      this.startAggregation();
    }
  }

  /**
   * 获取指定任务的进度数据
   */
  getProgress(taskId: string): ProgressData | undefined {
    return this.progressBuffer.get(taskId);
  }

  /**
   * 获取所有进度数据
   */
  getAllProgress(): ProgressData[] {
    return Array.from(this.progressBuffer.values());
  }

  /**
   * 清空进度缓冲区
   */
  clearBuffer(): void {
    this.progressBuffer.clear();
  }

  /**
   * 调度下一次刷新
   */
  private scheduleNextFlush(): void {
    this.timer = setTimeout(() => {
      this.flushUpdates();
      
      // 如果还有数据需要处理，继续调度
      if (this.progressBuffer.size > 0 && this.isRunning) {
        this.scheduleNextFlush();
      } else {
        this.isRunning = false;
      }
    }, this.updateInterval);
  }

  /**
   * 刷新进度更新
   */
  private flushUpdates(): void {
    if (this.progressBuffer.size === 0) {
      return;
    }

    const updates = Array.from(this.progressBuffer.values());
    const aggregatedProgress: AggregatedProgress = {
      updates,
      timestamp: Date.now(),
    };

    // 发送聚合的进度更新事件
    this.emit("progress-aggregated", aggregatedProgress);

    // 清空缓冲区
    this.progressBuffer.clear();
  }
}
