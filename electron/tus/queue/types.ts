// 队列管理相关类型定义

// 队列中的任务项
export interface QueuedTask {
  taskId: string;
  priority: number;
  addedAt: Date;
  retryCount: number;
  maxRetries: number;
  metadata?: Record<string, string>;
}

// 队列状态
export type QueueStatus = "idle" | "running" | "paused" | "error";

// 队列统计信息
export interface QueueStats {
  status: QueueStatus;
  totalTasks: number;
  pendingTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  maxConcurrent: number;
  currentConcurrent: number;
}

// 进度数据
export interface ProgressData {
  taskId: string;
  progress: number;
  bytesUploaded: number;
  bytesTotal: number;
  uploadSpeed: number;
  remainingTime: number;
  timestamp: number;
}

// 聚合的进度更新
export interface AggregatedProgress {
  updates: ProgressData[];
  timestamp: number;
}

// 队列事件类型
export interface QueueEvents {
  "queue-status-changed": (status: QueueStatus) => void;
  "queue-stats-updated": (stats: QueueStats) => void;
  "task-queued": (taskId: string, queuedTask: QueuedTask) => void;
  "task-dequeued": (taskId: string) => void;
  "task-started": (taskId: string) => void;
  "task-retry": (taskId: string, retryCount: number) => void;
  "progress-aggregated": (updates: AggregatedProgress) => void;
}

// 队列配置
export interface QueueConfig {
  maxConcurrent: number;
  maxRetries: number;
  retryDelay: number;
  progressUpdateInterval: number;
  storageUpdateInterval: number;
  enablePriority: boolean;
}

// 任务优先级枚举
export enum TaskPriority {
  LOW = 1,
  NORMAL = 5,
  HIGH = 10,
  URGENT = 20
}

// 队列操作结果
export interface QueueOperationResult {
  success: boolean;
  message?: string;
  data?: any;
}
