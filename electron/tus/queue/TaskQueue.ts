import { EventEmitter } from "events";
import type { QueuedTask, QueueStatus, QueueEvents, TaskPriority } from "./types";

/**
 * 任务队列类
 * 管理待执行的上传任务，支持优先级排序和队列操作
 */
export class TaskQueue extends EventEmitter {
  private queue: QueuedTask[] = [];
  private status: QueueStatus = "idle";
  private paused: boolean = false;

  constructor() {
    super();
  }

  /**
   * 将任务加入队列
   */
  enqueue(taskId: string, priority: number = TaskPriority.NORMAL, maxRetries: number = 3, metadata?: Record<string, string>): void {
    console.log(`📋 Enqueuing task: ${taskId}, priority: ${priority}, current queue size: ${this.queue.length}`);

    const queuedTask: QueuedTask = {
      taskId,
      priority,
      addedAt: new Date(),
      retryCount: 0,
      maxRetries,
      metadata,
    };

    // 按优先级插入队列（优先级高的在前）
    const insertIndex = this.findInsertPosition(priority);
    this.queue.splice(insertIndex, 0, queuedTask);

    console.log(`✅ Task enqueued: ${taskId}, new queue size: ${this.queue.length}, position: ${insertIndex}`);

    this.emit("task-queued", taskId, queuedTask);
    this.updateStatus();
  }

  /**
   * 从队列中取出下一个任务
   */
  dequeue(): QueuedTask | undefined {
    if (this.paused || this.queue.length === 0) {
      console.log(`🚫 Cannot dequeue: paused=${this.paused}, queueLength=${this.queue.length}`);
      return undefined;
    }

    const task = this.queue.shift();
    if (task) {
      console.log(`📤 Dequeued task: ${task.taskId}, remaining queue size: ${this.queue.length}`);
      this.emit("task-dequeued", task.taskId);
      this.updateStatus();
    }

    return task;
  }

  /**
   * 重新排队失败的任务（用于重试）
   */
  requeueForRetry(taskId: string): boolean {
    // 查找是否有对应的任务记录（可能在其他地方维护）
    // 这里简化处理，直接创建重试任务
    const existingTaskIndex = this.queue.findIndex((t) => t.taskId === taskId);

    if (existingTaskIndex >= 0) {
      const task = this.queue[existingTaskIndex];
      if (task.retryCount < task.maxRetries) {
        task.retryCount++;
        // 重试任务降低优先级
        task.priority = Math.max(1, task.priority - 1);

        // 重新排序
        this.queue.splice(existingTaskIndex, 1);
        const insertIndex = this.findInsertPosition(task.priority);
        this.queue.splice(insertIndex, 0, task);

        this.emit("task-retry", taskId, task.retryCount);
        return true;
      }
    }

    return false;
  }

  /**
   * 移除指定任务
   */
  remove(taskId: string): boolean {
    const index = this.queue.findIndex((task) => task.taskId === taskId);
    if (index >= 0) {
      this.queue.splice(index, 1);
      this.updateStatus();
      return true;
    }
    return false;
  }

  /**
   * 暂停队列
   */
  pause(): void {
    this.paused = true;
    this.updateStatus();
  }

  /**
   * 恢复队列
   */
  resume(): void {
    this.paused = false;
    this.updateStatus();
  }

  /**
   * 清空队列
   */
  clear(): void {
    const clearedTasks = this.queue.splice(0);
    clearedTasks.forEach((task) => {
      this.emit("task-dequeued", task.taskId);
    });
    this.updateStatus();
  }

  /**
   * 获取队列大小
   */
  size(): number {
    return this.queue.length;
  }

  /**
   * 检查队列是否为空
   */
  isEmpty(): boolean {
    return this.queue.length === 0;
  }

  /**
   * 检查队列是否暂停
   */
  isPaused(): boolean {
    return this.paused;
  }

  /**
   * 获取队列状态
   */
  getStatus(): QueueStatus {
    return this.status;
  }

  /**
   * 获取队列中的所有任务
   */
  getAllTasks(): QueuedTask[] {
    return [...this.queue];
  }

  /**
   * 获取指定任务
   */
  getTask(taskId: string): QueuedTask | undefined {
    return this.queue.find((task) => task.taskId === taskId);
  }

  /**
   * 查找任务插入位置（按优先级排序）
   */
  private findInsertPosition(priority: number): number {
    for (let i = 0; i < this.queue.length; i++) {
      if (this.queue[i].priority < priority) {
        return i;
      }
    }
    return this.queue.length;
  }

  /**
   * 更新队列状态
   */
  private updateStatus(): void {
    let newStatus: QueueStatus;

    if (this.paused) {
      newStatus = "paused";
    } else if (this.queue.length === 0) {
      newStatus = "idle";
    } else {
      newStatus = "running";
    }

    if (newStatus !== this.status) {
      this.status = newStatus;
      this.emit("queue-status-changed", this.status);
    }
  }
}
