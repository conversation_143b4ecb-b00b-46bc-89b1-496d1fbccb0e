import Store from "electron-store";
import type { UploadTask, StoreData } from "../types";

/**
 * 存储管理器
 * 实现批量存储更新和节流机制，减少磁盘IO操作
 */
export class StorageManager {
  private store: Store<StoreData>;
  private pendingUpdates: Map<string, UploadTask> = new Map();
  private updateInterval: number;
  private timer: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;

  constructor(store: Store<StoreData>, updateInterval: number = 2000) {
    this.store = store;
    this.updateInterval = updateInterval;
  }

  /**
   * 添加任务到待更新队列
   */
  scheduleTaskUpdate(task: UploadTask): void {
    this.pendingUpdates.set(task.id, { ...task });

    // 如果存储管理器没有运行，启动它
    if (!this.isRunning) {
      this.startBatchUpdates();
    }
  }

  /**
   * 立即保存指定任务
   */
  saveTaskImmediately(task: UploadTask): void {
    const tasks = this.store.get("tasks", {}) as Record<string, UploadTask>;
    tasks[task.id] = task;
    this.store.set("tasks", tasks);

    // 从待更新队列中移除
    this.pendingUpdates.delete(task.id);
  }

  /**
   * 立即保存所有待更新的任务
   */
  flushAll(): void {
    if (this.pendingUpdates.size > 0) {
      this.performBatchUpdate();
    }
  }

  /**
   * 移除任务
   */
  removeTask(taskId: string): void {
    // 从待更新队列中移除
    this.pendingUpdates.delete(taskId);

    // 从存储中移除
    const tasks = this.store.get("tasks", {}) as Record<string, UploadTask>;
    delete tasks[taskId];
    this.store.set("tasks", tasks);
  }

  /**
   * 获取待更新任务数量
   */
  getPendingCount(): number {
    return this.pendingUpdates.size;
  }

  /**
   * 设置更新间隔
   */
  setUpdateInterval(interval: number): void {
    this.updateInterval = interval;
    
    // 如果正在运行，重新调度
    if (this.isRunning) {
      this.stopBatchUpdates();
      this.startBatchUpdates();
    }
  }

  /**
   * 停止批量更新
   */
  stopBatchUpdates(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.isRunning = false;

    // 保存最后一次更新
    this.flushAll();
  }

  /**
   * 启动批量更新
   */
  private startBatchUpdates(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.scheduleNextUpdate();
  }

  /**
   * 调度下一次更新
   */
  private scheduleNextUpdate(): void {
    this.timer = setTimeout(() => {
      this.performBatchUpdate();
      
      // 如果还有待更新的数据，继续调度
      if (this.pendingUpdates.size > 0 && this.isRunning) {
        this.scheduleNextUpdate();
      } else {
        this.isRunning = false;
      }
    }, this.updateInterval);
  }

  /**
   * 执行批量更新
   */
  private performBatchUpdate(): void {
    if (this.pendingUpdates.size === 0) {
      return;
    }

    try {
      // 获取当前存储的任务
      const tasks = this.store.get("tasks", {}) as Record<string, UploadTask>;

      // 批量更新
      let updateCount = 0;
      for (const [taskId, task] of this.pendingUpdates) {
        tasks[taskId] = task;
        updateCount++;
      }

      // 一次性写入存储
      this.store.set("tasks", tasks);

      console.log(`批量存储更新完成: ${updateCount} 个任务`);

      // 清空待更新队列
      this.pendingUpdates.clear();
    } catch (error) {
      console.error("批量存储更新失败:", error);
    }
  }
}
